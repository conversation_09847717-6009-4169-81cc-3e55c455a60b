package edu.tsinghua.studentmis.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import edu.tsinghua.studentmis.analytics.entity.PersonalizedRecommendation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 个性化推荐Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface PersonalizedRecommendationMapper extends BaseMapper<PersonalizedRecommendation> {

    /**
     * 根据学生ID查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND deleted = 0 ORDER BY recommendation_score DESC, created_at DESC")
    List<PersonalizedRecommendation> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和推荐类型查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND recommendation_type = #{recommendationType} AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectByStudentIdAndType(@Param("studentId") Long studentId, 
                                                             @Param("recommendationType") String recommendationType);

    /**
     * 查询学生的有效推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND recommendation_status = 'ACTIVE' AND (expires_at IS NULL OR expires_at > NOW()) AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectActiveByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据推荐类型查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_type = #{recommendationType} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByRecommendationType(@Param("recommendationType") String recommendationType);

    /**
     * 查询高评分的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_score >= #{minScore} AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectByMinScore(@Param("minScore") Double minScore);

    /**
     * 查询学生未查看的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND is_viewed = 0 AND deleted = 0 ORDER BY recommendation_score DESC, created_at DESC")
    List<PersonalizedRecommendation> selectUnviewedByStudentId(@Param("studentId") Long studentId);

    /**
     * 查询学生已采纳的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND is_adopted = 1 AND deleted = 0 ORDER BY adopted_at DESC")
    List<PersonalizedRecommendation> selectAdoptedByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据推荐状态查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_status = #{status} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByRecommendationStatus(@Param("status") String status);

    /**
     * 查询即将过期的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE expires_at BETWEEN NOW() AND #{expiryTime} AND recommendation_status = 'ACTIVE' AND deleted = 0")
    List<PersonalizedRecommendation> selectExpiringRecommendations(@Param("expiryTime") LocalDateTime expiryTime);

    /**
     * 统计学生的推荐记录数量
     */
    @Select("SELECT COUNT(*) FROM personalized_recommendation WHERE student_id = #{studentId} AND deleted = 0")
    Integer countByStudentId(@Param("studentId") Long studentId);

    /**
     * 统计学生未查看的推荐数量
     */
    @Select("SELECT COUNT(*) FROM personalized_recommendation WHERE student_id = #{studentId} AND is_viewed = 0 AND deleted = 0")
    Integer countUnviewedByStudentId(@Param("studentId") Long studentId);

    /**
     * 更新推荐记录为已查看
     */
    @Update("UPDATE personalized_recommendation SET is_viewed = 1, viewed_at = NOW() WHERE id = #{id}")
    int updateAsViewed(@Param("id") Long id);

    /**
     * 更新推荐记录为已采纳
     */
    @Update("UPDATE personalized_recommendation SET is_adopted = 1, adopted_at = NOW(), recommendation_status = 'ADOPTED' WHERE id = #{id}")
    int updateAsAdopted(@Param("id") Long id);

    /**
     * 批量更新过期的推荐记录状态
     */
    @Update("UPDATE personalized_recommendation SET recommendation_status = 'EXPIRED' WHERE expires_at < NOW() AND recommendation_status = 'ACTIVE'")
    int updateExpiredRecommendations();

    /**
     * 根据算法查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE algorithm = #{algorithm} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByAlgorithm(@Param("algorithm") String algorithm);

    /**
     * 查询指定时间范围内的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE created_at >= #{startTime} AND created_at <= #{endTime} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                                      @Param("endTime") LocalDateTime endTime);
}
