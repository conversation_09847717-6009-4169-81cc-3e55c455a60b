package edu.tsinghua.studentmis.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import edu.tsinghua.studentmis.analytics.entity.PersonalizedRecommendation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 个性化推荐Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface PersonalizedRecommendationMapper extends BaseMapper<PersonalizedRecommendation> {

    /**
     * 根据学生ID查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND deleted = 0 ORDER BY recommendation_score DESC, created_at DESC")
    List<PersonalizedRecommendation> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和推荐类型查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND recommendation_type = #{recommendationType} AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectByStudentIdAndType(@Param("studentId") Long studentId, 
                                                             @Param("recommendationType") String recommendationType);

    /**
     * 查询学生的有效推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND recommendation_status = 'ACTIVE' AND (expires_at IS NULL OR expires_at > NOW()) AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectActiveByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据推荐类型查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_type = #{recommendationType} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByRecommendationType(@Param("recommendationType") String recommendationType);

    /**
     * 查询高评分的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_score >= #{minScore} AND deleted = 0 ORDER BY recommendation_score DESC")
    List<PersonalizedRecommendation> selectByMinScore(@Param("minScore") Double minScore);

    /**
     * 查询学生未查看的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND is_viewed = 0 AND deleted = 0 ORDER BY recommendation_score DESC, created_at DESC")
    List<PersonalizedRecommendation> selectUnviewedByStudentId(@Param("studentId") Long studentId);

    /**
     * 查询学生已采纳的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE student_id = #{studentId} AND is_adopted = 1 AND deleted = 0 ORDER BY adopted_at DESC")
    List<PersonalizedRecommendation> selectAdoptedByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据推荐状态查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE recommendation_status = #{status} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByRecommendationStatus(@Param("status") String status);

    /**
     * 查询即将过期的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE expires_at BETWEEN NOW() AND #{expiryTime} AND recommendation_status = 'ACTIVE' AND deleted = 0")
    List<PersonalizedRecommendation> selectExpiringRecommendations(@Param("expiryTime") LocalDateTime expiryTime);

    /**
     * 统计学生的推荐记录数量
     */
    @Select("SELECT COUNT(*) FROM personalized_recommendation WHERE student_id = #{studentId} AND deleted = 0")
    Integer countByStudentId(@Param("studentId") Long studentId);

    /**
     * 统计学生未查看的推荐数量
     */
    @Select("SELECT COUNT(*) FROM personalized_recommendation WHERE student_id = #{studentId} AND is_viewed = 0 AND deleted = 0")
    Integer countUnviewedByStudentId(@Param("studentId") Long studentId);

    /**
     * 更新推荐记录为已查看
     */
    @Update("UPDATE personalized_recommendation SET is_viewed = 1, viewed_at = NOW() WHERE id = #{id}")
    int updateAsViewed(@Param("id") Long id);

    /**
     * 更新推荐记录为已采纳
     */
    @Update("UPDATE personalized_recommendation SET is_adopted = 1, adopted_at = NOW(), recommendation_status = 'ADOPTED' WHERE id = #{id}")
    int updateAsAdopted(@Param("id") Long id);

    /**
     * 批量更新过期的推荐记录状态
     */
    @Update("UPDATE personalized_recommendation SET recommendation_status = 'EXPIRED' WHERE expires_at < NOW() AND recommendation_status = 'ACTIVE'")
    int updateExpiredRecommendations();

    /**
     * 根据算法查询推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE algorithm = #{algorithm} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByAlgorithm(@Param("algorithm") String algorithm);

    /**
     * 查询指定时间范围内的推荐记录
     */
    @Select("SELECT * FROM personalized_recommendation WHERE created_at >= #{startTime} AND created_at <= #{endTime} AND deleted = 0 ORDER BY created_at DESC")
    List<PersonalizedRecommendation> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    // Service期望的查询方法
    /**
     * 获取可选课程
     */
    @Select("SELECT * FROM course WHERE status = 'ACTIVE' AND student_id != #{studentId} ORDER BY course_name")
    List<Map<String, Object>> getAvailableCourses(@Param("studentId") Long studentId);

    /**
     * 获取学生当前课程
     */
    @Select("SELECT c.* FROM course c JOIN student_course sc ON c.id = sc.course_id WHERE sc.student_id = #{studentId} AND sc.status = 'ACTIVE'")
    List<Map<String, Object>> getCurrentCourses(@Param("studentId") Long studentId);

    /**
     * 获取课程资源
     */
    @Select("SELECT * FROM course_resource WHERE course_id = #{courseId} AND status = 'ACTIVE' ORDER BY resource_name")
    List<Map<String, Object>> getCourseResources(@Param("courseId") Long courseId);

    /**
     * 获取可参与活动
     */
    @Select("SELECT * FROM activity WHERE status = 'ACTIVE' AND registration_end_date > NOW() ORDER BY activity_name")
    List<Map<String, Object>> getAvailableActivities();

    /**
     * 获取学生课程历史
     */
    @Select("SELECT c.*, sc.score FROM course c JOIN student_course sc ON c.id = sc.course_id WHERE sc.student_id = #{studentId} AND sc.status = 'COMPLETED'")
    List<Map<String, Object>> getStudentCourseHistory(@Param("studentId") Long studentId);

    /**
     * 获取学生档案
     */
    @Select("SELECT * FROM student_profile WHERE student_id = #{studentId}")
    Map<String, Object> getStudentProfile(@Param("studentId") Long studentId);

    /**
     * 获取学生特征
     */
    @Select("SELECT * FROM student_features WHERE student_id = #{studentId}")
    Map<String, Object> getStudentFeatures(@Param("studentId") Long studentId);

    /**
     * 获取学生兴趣
     */
    @Select("SELECT * FROM student_interests WHERE student_id = #{studentId}")
    Map<String, Object> getStudentInterests(@Param("studentId") Long studentId);

    /**
     * 获取所有学生特征
     */
    @Select("SELECT * FROM student_features")
    List<Map<String, Object>> getAllStudentFeatures();
}
