-- =====================================================
-- 清华大学级学生成绩管理系统数据库设计 V2.0
-- 设计标准: 符合第三范式，支持微服务架构
-- 安全等级: 等保三级标准
-- 创建时间: 2025-06-19
-- =====================================================

-- 创建数据库
DROP DATABASE IF EXISTS studentmis_v2;
CREATE DATABASE studentmis_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE studentmis_v2;

-- =====================================================
-- 系统管理模块 - 用户权限体系
-- =====================================================

-- 系统用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    salt VARCHAR(32) NOT NULL COMMENT '密码盐值',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(11) COMMENT '手机号',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED', 'DELETED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_fail_count INT DEFAULT 0 COMMENT '登录失败次数',
    password_expire_time TIMESTAMP NULL COMMENT '密码过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    version INT DEFAULT 1 COMMENT '乐观锁版本号',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    data_scope ENUM('ALL', 'DEPT', 'DEPT_AND_SUB', 'SELF') DEFAULT 'SELF' COMMENT '数据权限范围',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '角色状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    INDEX idx_role_code (role_code),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '权限ID',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_name VARCHAR(100) NOT NULL COMMENT '权限名称',
    permission_type ENUM('MENU', 'BUTTON', 'API') NOT NULL COMMENT '权限类型',
    path VARCHAR(255) COMMENT '路由路径',
    component VARCHAR(255) COMMENT '组件路径',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '权限状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_permission_code (permission_code),
    INDEX idx_permission_type (permission_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    created_by BIGINT COMMENT '创建人',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permission(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 操作日志表
CREATE TABLE sys_operation_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    method VARCHAR(200) COMMENT '请求方法',
    params TEXT COMMENT '请求参数',
    result TEXT COMMENT '返回结果',
    ip VARCHAR(45) COMMENT '操作IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    execute_time INT COMMENT '执行时间(毫秒)',
    status ENUM('SUCCESS', 'FAILURE') DEFAULT 'SUCCESS' COMMENT '操作状态',
    error_msg TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation (operation),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表';

-- =====================================================
-- 组织架构模块
-- =====================================================

-- 院系表
CREATE TABLE org_department (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '院系ID',
    dept_code VARCHAR(20) NOT NULL UNIQUE COMMENT '院系编码',
    dept_name VARCHAR(100) NOT NULL COMMENT '院系名称',
    dept_type ENUM('COLLEGE', 'SCHOOL', 'DEPARTMENT') NOT NULL COMMENT '院系类型',
    parent_id BIGINT DEFAULT 0 COMMENT '上级院系ID',
    leader_name VARCHAR(50) COMMENT '负责人姓名',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(255) COMMENT '地址',
    description TEXT COMMENT '描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    INDEX idx_dept_code (dept_code),
    INDEX idx_parent_id (parent_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='院系表';

-- 专业表
CREATE TABLE org_major (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '专业ID',
    major_code VARCHAR(20) NOT NULL UNIQUE COMMENT '专业编码',
    major_name VARCHAR(100) NOT NULL COMMENT '专业名称',
    dept_id BIGINT NOT NULL COMMENT '所属院系ID',
    degree_type ENUM('BACHELOR', 'MASTER', 'DOCTOR') NOT NULL COMMENT '学位类型',
    duration INT NOT NULL COMMENT '学制(年)',
    credit_requirement INT NOT NULL COMMENT '毕业学分要求',
    description TEXT COMMENT '专业描述',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    INDEX idx_major_code (major_code),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    FOREIGN KEY (dept_id) REFERENCES org_department(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专业表';

-- 班级表
CREATE TABLE org_class (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '班级ID',
    class_code VARCHAR(20) NOT NULL UNIQUE COMMENT '班级编码',
    class_name VARCHAR(100) NOT NULL COMMENT '班级名称',
    major_id BIGINT NOT NULL COMMENT '所属专业ID',
    grade_year INT NOT NULL COMMENT '年级',
    class_teacher_id BIGINT COMMENT '班主任ID',
    max_students INT DEFAULT 50 COMMENT '最大学生数',
    current_students INT DEFAULT 0 COMMENT '当前学生数',
    status ENUM('ACTIVE', 'INACTIVE', 'GRADUATED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    INDEX idx_class_code (class_code),
    INDEX idx_major_id (major_id),
    INDEX idx_grade_year (grade_year),
    INDEX idx_class_teacher_id (class_teacher_id),
    INDEX idx_status (status),
    FOREIGN KEY (major_id) REFERENCES org_major(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班级表';

-- 学期表
CREATE TABLE org_semester (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '学期ID',
    semester_code VARCHAR(20) NOT NULL UNIQUE COMMENT '学期编码',
    semester_name VARCHAR(50) NOT NULL COMMENT '学期名称',
    academic_year VARCHAR(10) NOT NULL COMMENT '学年',
    semester_type ENUM('SPRING', 'SUMMER', 'AUTUMN', 'WINTER') NOT NULL COMMENT '学期类型',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    is_current BOOLEAN DEFAULT FALSE COMMENT '是否当前学期',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_semester_code (semester_code),
    INDEX idx_academic_year (academic_year),
    INDEX idx_is_current (is_current),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学期表';

-- =====================================================
-- 学生管理模块
-- =====================================================

-- 学生基本信息表
CREATE TABLE stu_basic_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '学生ID',
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    name_en VARCHAR(100) COMMENT '英文姓名',
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL COMMENT '性别',
    birth_date DATE NOT NULL COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
    nationality VARCHAR(50) DEFAULT '中国' COMMENT '国籍',
    ethnicity VARCHAR(20) COMMENT '民族',
    political_status VARCHAR(20) COMMENT '政治面貌',
    phone VARCHAR(11) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(11) COMMENT '紧急联系电话',
    admission_date DATE NOT NULL COMMENT '入学日期',
    graduation_date DATE COMMENT '毕业日期',
    status ENUM('ACTIVE', 'SUSPENDED', 'GRADUATED', 'DROPPED', 'TRANSFERRED') DEFAULT 'ACTIVE' COMMENT '学籍状态',
    major_id BIGINT NOT NULL COMMENT '专业ID',
    class_id BIGINT NOT NULL COMMENT '班级ID',
    dormitory VARCHAR(50) COMMENT '宿舍',
    photo_url VARCHAR(255) COMMENT '照片URL',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    version INT DEFAULT 1 COMMENT '乐观锁版本号',
    INDEX idx_student_id (student_id),
    INDEX idx_name (name),
    INDEX idx_id_card (id_card),
    INDEX idx_major_class (major_id, class_id),
    INDEX idx_status (status),
    INDEX idx_admission_date (admission_date),
    FOREIGN KEY (major_id) REFERENCES org_major(id),
    FOREIGN KEY (class_id) REFERENCES org_class(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生基本信息表';

-- 学生家庭信息表
CREATE TABLE stu_family_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '家庭信息ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    relation_type ENUM('FATHER', 'MOTHER', 'GUARDIAN', 'OTHER') NOT NULL COMMENT '关系类型',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(11) COMMENT '电话',
    occupation VARCHAR(100) COMMENT '职业',
    work_unit VARCHAR(200) COMMENT '工作单位',
    address VARCHAR(255) COMMENT '地址',
    is_primary BOOLEAN DEFAULT FALSE COMMENT '是否主要联系人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_relation_type (relation_type),
    INDEX idx_is_primary (is_primary),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生家庭信息表';

-- 学生奖惩记录表
CREATE TABLE stu_award_punishment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    type ENUM('AWARD', 'PUNISHMENT') NOT NULL COMMENT '类型',
    category VARCHAR(50) NOT NULL COMMENT '类别',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    description TEXT COMMENT '描述',
    level ENUM('SCHOOL', 'COLLEGE', 'DEPARTMENT', 'CLASS', 'NATIONAL', 'PROVINCIAL', 'MUNICIPAL') COMMENT '级别',
    award_date DATE NOT NULL COMMENT '获奖/处分日期',
    issuer VARCHAR(100) COMMENT '颁发机构',
    certificate_no VARCHAR(100) COMMENT '证书编号',
    amount DECIMAL(10,2) COMMENT '奖金金额',
    status ENUM('ACTIVE', 'REVOKED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    INDEX idx_student_id (student_id),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_award_date (award_date),
    INDEX idx_status (status),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生奖惩记录表';

-- 学生学业预警表
CREATE TABLE stu_academic_warning (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预警ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    warning_type ENUM('GPA_LOW', 'CREDIT_INSUFFICIENT', 'ATTENDANCE_LOW', 'COURSE_FAIL', 'OTHER') NOT NULL COMMENT '预警类型',
    warning_level ENUM('YELLOW', 'ORANGE', 'RED') NOT NULL COMMENT '预警级别',
    semester_id BIGINT NOT NULL COMMENT '学期ID',
    title VARCHAR(200) NOT NULL COMMENT '预警标题',
    description TEXT NOT NULL COMMENT '预警描述',
    trigger_condition TEXT COMMENT '触发条件',
    suggestion TEXT COMMENT '改进建议',
    warning_date DATE NOT NULL COMMENT '预警日期',
    status ENUM('ACTIVE', 'RESOLVED', 'IGNORED') DEFAULT 'ACTIVE' COMMENT '状态',
    resolve_date DATE COMMENT '解决日期',
    resolve_note TEXT COMMENT '解决说明',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_warning_type (warning_type),
    INDEX idx_warning_level (warning_level),
    INDEX idx_semester_id (semester_id),
    INDEX idx_status (status),
    INDEX idx_warning_date (warning_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (semester_id) REFERENCES org_semester(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生学业预警表';

-- =====================================================
-- 教师管理模块
-- =====================================================

-- 教师基本信息表
CREATE TABLE tea_basic_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '教师ID',
    teacher_id VARCHAR(20) UNIQUE NOT NULL COMMENT '工号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    name_en VARCHAR(100) COMMENT '英文姓名',
    gender ENUM('MALE', 'FEMALE', 'OTHER') NOT NULL COMMENT '性别',
    birth_date DATE NOT NULL COMMENT '出生日期',
    id_card VARCHAR(18) UNIQUE COMMENT '身份证号',
    nationality VARCHAR(50) DEFAULT '中国' COMMENT '国籍',
    ethnicity VARCHAR(20) COMMENT '民族',
    political_status VARCHAR(20) COMMENT '政治面貌',
    phone VARCHAR(11) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    office_phone VARCHAR(20) COMMENT '办公电话',
    office_address VARCHAR(255) COMMENT '办公地址',
    hire_date DATE NOT NULL COMMENT '入职日期',
    dept_id BIGINT NOT NULL COMMENT '所属院系ID',
    title ENUM('ASSISTANT', 'LECTURER', 'ASSOCIATE_PROFESSOR', 'PROFESSOR', 'OTHER') COMMENT '职称',
    degree ENUM('BACHELOR', 'MASTER', 'DOCTOR', 'OTHER') COMMENT '学历',
    specialty VARCHAR(200) COMMENT '专业特长',
    research_direction TEXT COMMENT '研究方向',
    status ENUM('ACTIVE', 'INACTIVE', 'RETIRED', 'RESIGNED') DEFAULT 'ACTIVE' COMMENT '状态',
    photo_url VARCHAR(255) COMMENT '照片URL',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    version INT DEFAULT 1 COMMENT '乐观锁版本号',
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_name (name),
    INDEX idx_dept_id (dept_id),
    INDEX idx_title (title),
    INDEX idx_status (status),
    INDEX idx_hire_date (hire_date),
    FOREIGN KEY (dept_id) REFERENCES org_department(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教师基本信息表';

-- =====================================================
-- 课程管理模块
-- =====================================================

-- 课程信息表
CREATE TABLE course_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '课程ID',
    course_code VARCHAR(20) UNIQUE NOT NULL COMMENT '课程编码',
    course_name VARCHAR(100) NOT NULL COMMENT '课程名称',
    course_name_en VARCHAR(200) COMMENT '英文课程名称',
    course_type ENUM('REQUIRED', 'ELECTIVE', 'PUBLIC_REQUIRED', 'PUBLIC_ELECTIVE') NOT NULL COMMENT '课程类型',
    credit DECIMAL(3,1) NOT NULL COMMENT '学分',
    theory_hours INT DEFAULT 0 COMMENT '理论学时',
    practice_hours INT DEFAULT 0 COMMENT '实践学时',
    total_hours INT NOT NULL COMMENT '总学时',
    dept_id BIGINT NOT NULL COMMENT '开课院系ID',
    description TEXT COMMENT '课程描述',
    objectives TEXT COMMENT '课程目标',
    prerequisites TEXT COMMENT '先修课程要求',
    textbook VARCHAR(255) COMMENT '教材',
    reference_books TEXT COMMENT '参考书目',
    assessment_method TEXT COMMENT '考核方式',
    status ENUM('ACTIVE', 'INACTIVE', 'ARCHIVED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    version INT DEFAULT 1 COMMENT '乐观锁版本号',
    INDEX idx_course_code (course_code),
    INDEX idx_course_name (course_name),
    INDEX idx_course_type (course_type),
    INDEX idx_dept_id (dept_id),
    INDEX idx_status (status),
    FOREIGN KEY (dept_id) REFERENCES org_department(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程信息表';

-- 课程安排表
CREATE TABLE course_schedule (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '课程安排ID',
    course_id BIGINT NOT NULL COMMENT '课程ID',
    semester_id BIGINT NOT NULL COMMENT '学期ID',
    teacher_id BIGINT NOT NULL COMMENT '任课教师ID',
    class_code VARCHAR(50) NOT NULL COMMENT '教学班编码',
    class_name VARCHAR(100) NOT NULL COMMENT '教学班名称',
    max_students INT DEFAULT 50 COMMENT '最大选课人数',
    current_students INT DEFAULT 0 COMMENT '当前选课人数',
    classroom VARCHAR(50) COMMENT '教室',
    schedule_time TEXT COMMENT '上课时间安排',
    exam_type ENUM('WRITTEN', 'ORAL', 'PRACTICAL', 'PAPER', 'OTHER') COMMENT '考试类型',
    exam_date DATETIME COMMENT '考试时间',
    exam_location VARCHAR(100) COMMENT '考试地点',
    status ENUM('PLANNING', 'OPEN', 'CLOSED', 'COMPLETED', 'CANCELLED') DEFAULT 'PLANNING' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT NOT NULL COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    UNIQUE KEY uk_course_semester_class (course_id, semester_id, class_code),
    INDEX idx_course_id (course_id),
    INDEX idx_semester_id (semester_id),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_status (status),
    FOREIGN KEY (course_id) REFERENCES course_info(id),
    FOREIGN KEY (semester_id) REFERENCES org_semester(id),
    FOREIGN KEY (teacher_id) REFERENCES tea_basic_info(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程安排表';

-- 选课记录表
CREATE TABLE course_selection (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '选课记录ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    schedule_id BIGINT NOT NULL COMMENT '课程安排ID',
    selection_type ENUM('NORMAL', 'MAKEUP', 'RETAKE') DEFAULT 'NORMAL' COMMENT '选课类型',
    selection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '选课时间',
    status ENUM('SELECTED', 'DROPPED', 'COMPLETED') DEFAULT 'SELECTED' COMMENT '选课状态',
    drop_time TIMESTAMP NULL COMMENT '退课时间',
    drop_reason VARCHAR(255) COMMENT '退课原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_student_schedule (student_id, schedule_id),
    INDEX idx_student_id (student_id),
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_selection_type (selection_type),
    INDEX idx_status (status),
    INDEX idx_selection_time (selection_time),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (schedule_id) REFERENCES course_schedule(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='选课记录表';

-- =====================================================
-- 成绩管理模块
-- =====================================================

-- 成绩记录表
CREATE TABLE grade_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '成绩记录ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    schedule_id BIGINT NOT NULL COMMENT '课程安排ID',
    grade_type ENUM('REGULAR', 'MAKEUP', 'RETAKE') DEFAULT 'REGULAR' COMMENT '成绩类型',
    usual_score DECIMAL(5,2) COMMENT '平时成绩',
    midterm_score DECIMAL(5,2) COMMENT '期中成绩',
    final_score DECIMAL(5,2) COMMENT '期末成绩',
    total_score DECIMAL(5,2) COMMENT '总成绩',
    grade_point DECIMAL(3,2) COMMENT '绩点',
    letter_grade VARCHAR(2) COMMENT '等级成绩',
    is_pass BOOLEAN DEFAULT FALSE COMMENT '是否通过',
    rank_in_class INT COMMENT '班级排名',
    percentile DECIMAL(5,2) COMMENT '百分位数',
    exam_date DATE COMMENT '考试日期',
    input_by BIGINT NOT NULL COMMENT '录入人',
    input_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '录入时间',
    audit_by BIGINT COMMENT '审核人',
    audit_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING' COMMENT '审核状态',
    audit_time TIMESTAMP NULL COMMENT '审核时间',
    audit_comment VARCHAR(255) COMMENT '审核意见',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '乐观锁版本号',
    UNIQUE KEY uk_student_schedule_type (student_id, schedule_id, grade_type),
    INDEX idx_student_id (student_id),
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_grade_type (grade_type),
    INDEX idx_audit_status (audit_status),
    INDEX idx_is_pass (is_pass),
    INDEX idx_input_time (input_time),
    INDEX idx_exam_date (exam_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (schedule_id) REFERENCES course_schedule(id),
    FOREIGN KEY (input_by) REFERENCES tea_basic_info(id),
    FOREIGN KEY (audit_by) REFERENCES tea_basic_info(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成绩记录表';

-- GPA计算表
CREATE TABLE grade_gpa_calculation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'GPA计算ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    semester_id BIGINT COMMENT '学期ID(NULL表示总GPA)',
    total_credits DECIMAL(6,1) NOT NULL COMMENT '总学分',
    earned_credits DECIMAL(6,1) NOT NULL COMMENT '获得学分',
    gpa DECIMAL(4,3) NOT NULL COMMENT 'GPA',
    weighted_score DECIMAL(6,2) NOT NULL COMMENT '加权平均分',
    rank_in_major INT COMMENT '专业排名',
    rank_in_grade INT COMMENT '年级排名',
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_student_semester (student_id, semester_id),
    INDEX idx_student_id (student_id),
    INDEX idx_semester_id (semester_id),
    INDEX idx_gpa (gpa),
    INDEX idx_calculation_date (calculation_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (semester_id) REFERENCES org_semester(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='GPA计算表';

-- 成绩申诉表
CREATE TABLE grade_appeal (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '申诉ID',
    grade_record_id BIGINT NOT NULL COMMENT '成绩记录ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    appeal_reason TEXT NOT NULL COMMENT '申诉理由',
    evidence_files TEXT COMMENT '证据文件',
    appeal_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申诉时间',
    handler_id BIGINT COMMENT '处理人ID',
    handle_time TIMESTAMP NULL COMMENT '处理时间',
    handle_result ENUM('APPROVED', 'REJECTED', 'PENDING') DEFAULT 'PENDING' COMMENT '处理结果',
    handle_comment TEXT COMMENT '处理意见',
    original_score DECIMAL(5,2) COMMENT '原成绩',
    adjusted_score DECIMAL(5,2) COMMENT '调整后成绩',
    status ENUM('SUBMITTED', 'PROCESSING', 'COMPLETED', 'CANCELLED') DEFAULT 'SUBMITTED' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_grade_record_id (grade_record_id),
    INDEX idx_student_id (student_id),
    INDEX idx_handler_id (handler_id),
    INDEX idx_handle_result (handle_result),
    INDEX idx_status (status),
    INDEX idx_appeal_time (appeal_time),
    FOREIGN KEY (grade_record_id) REFERENCES grade_record(id),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id),
    FOREIGN KEY (handler_id) REFERENCES tea_basic_info(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成绩申诉表';

-- 成绩统计表
CREATE TABLE grade_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    schedule_id BIGINT NOT NULL COMMENT '课程安排ID',
    total_students INT NOT NULL COMMENT '总人数',
    submitted_students INT NOT NULL COMMENT '已提交成绩人数',
    pass_students INT NOT NULL COMMENT '通过人数',
    pass_rate DECIMAL(5,2) NOT NULL COMMENT '通过率',
    excellent_students INT NOT NULL COMMENT '优秀人数',
    excellent_rate DECIMAL(5,2) NOT NULL COMMENT '优秀率',
    average_score DECIMAL(5,2) NOT NULL COMMENT '平均分',
    highest_score DECIMAL(5,2) NOT NULL COMMENT '最高分',
    lowest_score DECIMAL(5,2) NOT NULL COMMENT '最低分',
    standard_deviation DECIMAL(5,2) COMMENT '标准差',
    score_distribution JSON COMMENT '分数分布',
    calculation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_schedule (schedule_id),
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_pass_rate (pass_rate),
    INDEX idx_average_score (average_score),
    INDEX idx_calculation_time (calculation_time),
    FOREIGN KEY (schedule_id) REFERENCES course_schedule(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成绩统计表';

-- =====================================================
-- 数据分析与智能化模块
-- =====================================================

-- 学习行为分析表
CREATE TABLE analytics_learning_behavior (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '行为分析ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    semester_id BIGINT NOT NULL COMMENT '学期ID',
    login_frequency INT DEFAULT 0 COMMENT '登录频次',
    study_duration INT DEFAULT 0 COMMENT '学习时长(分钟)',
    assignment_completion_rate DECIMAL(5,2) DEFAULT 0 COMMENT '作业完成率',
    attendance_rate DECIMAL(5,2) DEFAULT 0 COMMENT '出勤率',
    forum_participation INT DEFAULT 0 COMMENT '论坛参与次数',
    resource_access_count INT DEFAULT 0 COMMENT '资源访问次数',
    quiz_attempts INT DEFAULT 0 COMMENT '测验尝试次数',
    help_seeking_frequency INT DEFAULT 0 COMMENT '求助频次',
    peer_interaction_score DECIMAL(5,2) DEFAULT 0 COMMENT '同伴互动得分',
    learning_pattern VARCHAR(50) COMMENT '学习模式',
    engagement_level ENUM('LOW', 'MEDIUM', 'HIGH') COMMENT '参与度等级',
    risk_level ENUM('LOW', 'MEDIUM', 'HIGH') COMMENT '风险等级',
    analysis_date DATE NOT NULL COMMENT '分析日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_student_semester_date (student_id, semester_id, analysis_date),
    INDEX idx_student_id (student_id),
    INDEX idx_semester_id (semester_id),
    INDEX idx_engagement_level (engagement_level),
    INDEX idx_risk_level (risk_level),
    INDEX idx_analysis_date (analysis_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (semester_id) REFERENCES org_semester(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学习行为分析表';

-- 成绩预测表
CREATE TABLE analytics_grade_prediction (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '预测ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    schedule_id BIGINT NOT NULL COMMENT '课程安排ID',
    prediction_type ENUM('MIDTERM', 'FINAL', 'TOTAL') NOT NULL COMMENT '预测类型',
    predicted_score DECIMAL(5,2) NOT NULL COMMENT '预测成绩',
    confidence_level DECIMAL(5,2) NOT NULL COMMENT '置信度',
    prediction_factors JSON COMMENT '预测因子',
    model_version VARCHAR(20) NOT NULL COMMENT '模型版本',
    prediction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '预测时间',
    actual_score DECIMAL(5,2) COMMENT '实际成绩',
    prediction_accuracy DECIMAL(5,2) COMMENT '预测准确度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_schedule_id (schedule_id),
    INDEX idx_prediction_type (prediction_type),
    INDEX idx_confidence_level (confidence_level),
    INDEX idx_prediction_date (prediction_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE,
    FOREIGN KEY (schedule_id) REFERENCES course_schedule(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='成绩预测表';

-- 个性化推荐表
CREATE TABLE analytics_recommendation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    recommendation_type ENUM('COURSE', 'STUDY_METHOD', 'RESOURCE', 'ACTIVITY') NOT NULL COMMENT '推荐类型',
    item_id BIGINT COMMENT '推荐项目ID',
    item_name VARCHAR(200) NOT NULL COMMENT '推荐项目名称',
    recommendation_score DECIMAL(5,2) NOT NULL COMMENT '推荐分数',
    reason TEXT COMMENT '推荐理由',
    algorithm_used VARCHAR(50) NOT NULL COMMENT '使用算法',
    is_clicked BOOLEAN DEFAULT FALSE COMMENT '是否点击',
    is_adopted BOOLEAN DEFAULT FALSE COMMENT '是否采纳',
    feedback_score INT COMMENT '反馈评分(1-5)',
    feedback_comment TEXT COMMENT '反馈意见',
    recommendation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '推荐时间',
    expire_date TIMESTAMP COMMENT '过期时间',
    status ENUM('ACTIVE', 'CLICKED', 'ADOPTED', 'IGNORED', 'EXPIRED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_student_id (student_id),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_recommendation_score (recommendation_score),
    INDEX idx_status (status),
    INDEX idx_recommendation_date (recommendation_date),
    FOREIGN KEY (student_id) REFERENCES stu_basic_info(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个性化推荐表';

-- =====================================================
-- 消息通知模块
-- =====================================================

-- 系统通知表
CREATE TABLE sys_notification (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    notification_type ENUM('SYSTEM', 'ACADEMIC', 'GRADE', 'COURSE', 'ACTIVITY', 'WARNING') NOT NULL COMMENT '通知类型',
    priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') DEFAULT 'MEDIUM' COMMENT '优先级',
    target_type ENUM('ALL', 'ROLE', 'DEPARTMENT', 'MAJOR', 'CLASS', 'INDIVIDUAL') NOT NULL COMMENT '目标类型',
    target_ids TEXT COMMENT '目标ID列表',
    sender_id BIGINT COMMENT '发送人ID',
    send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    expire_time TIMESTAMP COMMENT '过期时间',
    is_popup BOOLEAN DEFAULT FALSE COMMENT '是否弹窗',
    is_email BOOLEAN DEFAULT FALSE COMMENT '是否邮件',
    is_sms BOOLEAN DEFAULT FALSE COMMENT '是否短信',
    attachment_urls TEXT COMMENT '附件URL列表',
    status ENUM('DRAFT', 'SENT', 'CANCELLED') DEFAULT 'DRAFT' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人',
    INDEX idx_notification_type (notification_type),
    INDEX idx_priority (priority),
    INDEX idx_target_type (target_type),
    INDEX idx_sender_id (sender_id),
    INDEX idx_send_time (send_time),
    INDEX idx_status (status),
    FOREIGN KEY (sender_id) REFERENCES sys_user(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知表';

-- 用户通知记录表
CREATE TABLE sys_user_notification (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户通知记录ID',
    notification_id BIGINT NOT NULL COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_time TIMESTAMP NULL COMMENT '阅读时间',
    is_deleted BOOLEAN DEFAULT FALSE COMMENT '是否删除',
    delete_time TIMESTAMP NULL COMMENT '删除时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_notification_user (notification_id, user_id),
    INDEX idx_notification_id (notification_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (notification_id) REFERENCES sys_notification(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知记录表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认角色
INSERT INTO sys_role (role_code, role_name, description, data_scope, created_by) VALUES
('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 'ALL', 1),
('SYSTEM_ADMIN', '系统管理员', '系统管理员，负责系统维护', 'ALL', 1),
('DEPT_ADMIN', '院系管理员', '院系管理员，管理本院系数据', 'DEPT_AND_SUB', 1),
('TEACHER', '教师', '任课教师，管理课程和成绩', 'SELF', 1),
('CLASS_TEACHER', '班主任', '班主任，管理班级学生', 'DEPT', 1),
('STUDENT', '学生', '在校学生，查看个人信息和成绩', 'SELF', 1),
('PARENT', '家长', '学生家长，查看子女学习情况', 'SELF', 1);

-- 插入默认权限
INSERT INTO sys_permission (parent_id, permission_code, permission_name, permission_type, path, sort_order) VALUES
(0, 'SYSTEM', '系统管理', 'MENU', '/system', 1),
(1, 'USER_MANAGE', '用户管理', 'MENU', '/system/user', 1),
(1, 'ROLE_MANAGE', '角色管理', 'MENU', '/system/role', 2),
(1, 'PERMISSION_MANAGE', '权限管理', 'MENU', '/system/permission', 3),
(1, 'LOG_MANAGE', '日志管理', 'MENU', '/system/log', 4),
(0, 'ACADEMIC', '教务管理', 'MENU', '/academic', 2),
(6, 'STUDENT_MANAGE', '学生管理', 'MENU', '/academic/student', 1),
(6, 'TEACHER_MANAGE', '教师管理', 'MENU', '/academic/teacher', 2),
(6, 'COURSE_MANAGE', '课程管理', 'MENU', '/academic/course', 3),
(6, 'GRADE_MANAGE', '成绩管理', 'MENU', '/academic/grade', 4),
(0, 'ANALYTICS', '数据分析', 'MENU', '/analytics', 3),
(11, 'STUDENT_ANALYTICS', '学生分析', 'MENU', '/analytics/student', 1),
(11, 'GRADE_ANALYTICS', '成绩分析', 'MENU', '/analytics/grade', 2),
(11, 'PREDICTION', '预测分析', 'MENU', '/analytics/prediction', 3);

-- 插入超级管理员用户
INSERT INTO sys_user (username, password_hash, salt, real_name, email, phone, status, created_by) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhiXoHKYp1qYNpUvpNhDUu', 'salt123', '系统管理员', '<EMAIL>', '13800138000', 'ACTIVE', 1);

-- 为超级管理员分配角色
INSERT INTO sys_user_role (user_id, role_id, created_by) VALUES (1, 1, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO sys_role_permission (role_id, permission_id, created_by)
SELECT 1, id, 1 FROM sys_permission;

-- 插入默认院系
INSERT INTO org_department (dept_code, dept_name, dept_type, leader_name, phone, email, created_by) VALUES
('CS', '计算机科学与技术系', 'DEPARTMENT', '张教授', '010-62785678', '<EMAIL>', 1),
('EE', '电子工程系', 'DEPARTMENT', '李教授', '010-62785679', '<EMAIL>', 1),
('MATH', '数学科学系', 'DEPARTMENT', '王教授', '010-62785680', '<EMAIL>', 1),
('PHYS', '物理系', 'DEPARTMENT', '赵教授', '010-62785681', '<EMAIL>', 1);

-- 插入默认专业
INSERT INTO org_major (major_code, major_name, dept_id, degree_type, duration, credit_requirement, created_by) VALUES
('CS01', '计算机科学与技术', 1, 'BACHELOR', 4, 160, 1),
('CS02', '软件工程', 1, 'BACHELOR', 4, 160, 1),
('CS03', '人工智能', 1, 'BACHELOR', 4, 160, 1),
('EE01', '电子信息工程', 2, 'BACHELOR', 4, 160, 1),
('EE02', '通信工程', 2, 'BACHELOR', 4, 160, 1),
('MATH01', '数学与应用数学', 3, 'BACHELOR', 4, 160, 1),
('PHYS01', '物理学', 4, 'BACHELOR', 4, 160, 1);

-- 插入当前学期
INSERT INTO org_semester (semester_code, semester_name, academic_year, semester_type, start_date, end_date, is_current, created_by) VALUES
('2024-2025-2', '2024-2025学年春季学期', '2024-2025', 'SPRING', '2025-02-24', '2025-07-14', TRUE, 1);

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;
