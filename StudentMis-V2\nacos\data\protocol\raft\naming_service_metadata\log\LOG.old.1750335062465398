2025/06/19-19:27:56.201068 4be8 RocksDB version: 7.7.3
2025/06/19-19:27:56.201139 4be8 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:27:56.201153 4be8 Compile date 2022-10-24 17:17:55
2025/06/19-19:27:56.201162 4be8 DB SUMMARY
2025/06/19-19:27:56.201171 4be8 DB Session ID:  DEIGZIB5FYX57QXAYUQE
2025/06/19-19:27:56.201723 4be8 CURRENT file:  CURRENT
2025/06/19-19:27:56.201741 4be8 IDENTITY file:  IDENTITY
2025/06/19-19:27:56.201837 4be8 MANIFEST file:  MANIFEST-000005 size: 119 Bytes
2025/06/19-19:27:56.201850 4be8 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log dir, Total Num: 0, files: 
2025/06/19-19:27:56.201859 4be8 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log: 000004.log size: 110 ; 
2025/06/19-19:27:56.201993 4be8                         Options.error_if_exists: 0
2025/06/19-19:27:56.201999 4be8                       Options.create_if_missing: 1
2025/06/19-19:27:56.202003 4be8                         Options.paranoid_checks: 1
2025/06/19-19:27:56.202005 4be8             Options.flush_verify_memtable_count: 1
2025/06/19-19:27:56.202008 4be8                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:27:56.202011 4be8        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:27:56.202014 4be8                                     Options.env: 00000282D34347A0
2025/06/19-19:27:56.202018 4be8                                      Options.fs: WinFS
2025/06/19-19:27:56.202021 4be8                                Options.info_log: 00000282FEE72E40
2025/06/19-19:27:56.202024 4be8                Options.max_file_opening_threads: 16
2025/06/19-19:27:56.202027 4be8                              Options.statistics: 00000282FE5C7DA0
2025/06/19-19:27:56.202030 4be8                               Options.use_fsync: 0
2025/06/19-19:27:56.202032 4be8                       Options.max_log_file_size: 0
2025/06/19-19:27:56.202035 4be8                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:27:56.202038 4be8                   Options.log_file_time_to_roll: 0
2025/06/19-19:27:56.202042 4be8                       Options.keep_log_file_num: 100
2025/06/19-19:27:56.202045 4be8                    Options.recycle_log_file_num: 0
2025/06/19-19:27:56.202048 4be8                         Options.allow_fallocate: 1
2025/06/19-19:27:56.202051 4be8                        Options.allow_mmap_reads: 0
2025/06/19-19:27:56.202056 4be8                       Options.allow_mmap_writes: 0
2025/06/19-19:27:56.202059 4be8                        Options.use_direct_reads: 0
2025/06/19-19:27:56.202062 4be8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:27:56.202064 4be8          Options.create_missing_column_families: 1
2025/06/19-19:27:56.202067 4be8                              Options.db_log_dir: 
2025/06/19-19:27:56.202070 4be8                                 Options.wal_dir: 
2025/06/19-19:27:56.202073 4be8                Options.table_cache_numshardbits: 6
2025/06/19-19:27:56.202075 4be8                         Options.WAL_ttl_seconds: 0
2025/06/19-19:27:56.202078 4be8                       Options.WAL_size_limit_MB: 0
2025/06/19-19:27:56.202081 4be8                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:27:56.202084 4be8             Options.manifest_preallocation_size: 4194304
2025/06/19-19:27:56.202087 4be8                     Options.is_fd_close_on_exec: 1
2025/06/19-19:27:56.202089 4be8                   Options.advise_random_on_open: 1
2025/06/19-19:27:56.202092 4be8                    Options.db_write_buffer_size: 0
2025/06/19-19:27:56.202095 4be8                    Options.write_buffer_manager: 00000282D3435790
2025/06/19-19:27:56.202098 4be8         Options.access_hint_on_compaction_start: 1
2025/06/19-19:27:56.202100 4be8           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:27:56.202103 4be8                      Options.use_adaptive_mutex: 0
2025/06/19-19:27:56.202130 4be8                            Options.rate_limiter: 0000000000000000
2025/06/19-19:27:56.202135 4be8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:27:56.202138 4be8                       Options.wal_recovery_mode: 2
2025/06/19-19:27:56.202141 4be8                  Options.enable_thread_tracking: 0
2025/06/19-19:27:56.202143 4be8                  Options.enable_pipelined_write: 0
2025/06/19-19:27:56.202146 4be8                  Options.unordered_write: 0
2025/06/19-19:27:56.202149 4be8         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:27:56.202151 4be8      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:27:56.202154 4be8             Options.write_thread_max_yield_usec: 100
2025/06/19-19:27:56.202157 4be8            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:27:56.202160 4be8                               Options.row_cache: None
2025/06/19-19:27:56.202162 4be8                              Options.wal_filter: None
2025/06/19-19:27:56.202165 4be8             Options.avoid_flush_during_recovery: 0
2025/06/19-19:27:56.202168 4be8             Options.allow_ingest_behind: 0
2025/06/19-19:27:56.202171 4be8             Options.two_write_queues: 0
2025/06/19-19:27:56.202173 4be8             Options.manual_wal_flush: 0
2025/06/19-19:27:56.202176 4be8             Options.wal_compression: 0
2025/06/19-19:27:56.202179 4be8             Options.atomic_flush: 0
2025/06/19-19:27:56.202181 4be8             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:27:56.202184 4be8                 Options.persist_stats_to_disk: 0
2025/06/19-19:27:56.202187 4be8                 Options.write_dbid_to_manifest: 0
2025/06/19-19:27:56.202189 4be8                 Options.log_readahead_size: 0
2025/06/19-19:27:56.202192 4be8                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:27:56.202195 4be8                 Options.best_efforts_recovery: 0
2025/06/19-19:27:56.202198 4be8                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:27:56.202201 4be8            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:27:56.202203 4be8             Options.allow_data_in_errors: 0
2025/06/19-19:27:56.202206 4be8             Options.db_host_id: __hostname__
2025/06/19-19:27:56.202209 4be8             Options.enforce_single_del_contracts: true
2025/06/19-19:27:56.202212 4be8             Options.max_background_jobs: 2
2025/06/19-19:27:56.202214 4be8             Options.max_background_compactions: 4
2025/06/19-19:27:56.202217 4be8             Options.max_subcompactions: 1
2025/06/19-19:27:56.202220 4be8             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:27:56.202222 4be8           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:27:56.202225 4be8             Options.delayed_write_rate : 16777216
2025/06/19-19:27:56.202228 4be8             Options.max_total_wal_size: 1073741824
2025/06/19-19:27:56.202231 4be8             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:27:56.202234 4be8                   Options.stats_dump_period_sec: 600
2025/06/19-19:27:56.202237 4be8                 Options.stats_persist_period_sec: 600
2025/06/19-19:27:56.202240 4be8                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:27:56.202242 4be8                          Options.max_open_files: -1
2025/06/19-19:27:56.202245 4be8                          Options.bytes_per_sync: 0
2025/06/19-19:27:56.202248 4be8                      Options.wal_bytes_per_sync: 0
2025/06/19-19:27:56.202251 4be8                   Options.strict_bytes_per_sync: 0
2025/06/19-19:27:56.202254 4be8       Options.compaction_readahead_size: 0
2025/06/19-19:27:56.202257 4be8                  Options.max_background_flushes: 1
2025/06/19-19:27:56.202260 4be8 Compression algorithms supported:
2025/06/19-19:27:56.202264 4be8 	kZSTD supported: 1
2025/06/19-19:27:56.202267 4be8 	kSnappyCompression supported: 1
2025/06/19-19:27:56.202270 4be8 	kBZip2Compression supported: 0
2025/06/19-19:27:56.202273 4be8 	kZlibCompression supported: 1
2025/06/19-19:27:56.202301 4be8 	kLZ4Compression supported: 1
2025/06/19-19:27:56.202305 4be8 	kXpressCompression supported: 0
2025/06/19-19:27:56.202309 4be8 	kLZ4HCCompression supported: 1
2025/06/19-19:27:56.202311 4be8 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:27:56.202315 4be8 Fast CRC32 supported: Not supported on x86
2025/06/19-19:27:56.202318 4be8 DMutex implementation: std::mutex
2025/06/19-19:27:56.203379 4be8 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000005
2025/06/19-19:27:56.203604 4be8 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:27:56.203611 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.203615 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.203617 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.203620 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.203623 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.203625 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.203628 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.203657 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282FE480080)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D2ED60A0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.203661 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.203664 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.203667 4be8          Options.compression: Snappy
2025/06/19-19:27:56.203670 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.203673 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.203676 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.203678 4be8             Options.num_levels: 7
2025/06/19-19:27:56.203681 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.203683 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.203686 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.203689 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.203692 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.203695 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.203698 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.203700 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.203703 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.203706 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.203709 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.203714 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.203718 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.203721 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.203724 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.203726 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.203729 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.203732 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.203734 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.203737 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.203740 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.203742 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.203745 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.203748 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.203750 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.203753 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.203756 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.203758 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.203761 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.203765 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.203768 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.203771 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.203773 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.203776 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.203779 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.203782 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.203785 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.203787 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.203790 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.203794 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.203797 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.203800 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.203808 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.203812 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.203814 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.203817 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.203820 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.203823 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.203826 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.203829 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.203832 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.203835 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.203839 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.203842 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.203845 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.203848 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.203891 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.203895 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.203898 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.203901 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.203904 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.203907 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.203909 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.203912 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.203915 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.203917 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.203920 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.203923 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.203926 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.203928 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.203931 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.203934 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.203937 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.203940 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.203943 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.203946 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.203949 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.206125 4be8 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:27:56.206136 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.206140 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.206142 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.206145 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.206148 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.206151 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.206154 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.206180 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282FE480080)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D2ED60A0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.206184 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.206187 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.206189 4be8          Options.compression: Snappy
2025/06/19-19:27:56.206192 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.206195 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.206201 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.206205 4be8             Options.num_levels: 7
2025/06/19-19:27:56.206207 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.206210 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.206213 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.206215 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.206218 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.206221 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.206224 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.206227 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.206229 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.206232 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.206235 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.206238 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.206241 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.206243 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.206246 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.206249 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.206251 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.206254 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.206257 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.206260 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.206262 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.206265 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.206268 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.206270 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.206273 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.206276 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.206279 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.206281 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.206284 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.206287 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.206290 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.206293 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.206296 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.206298 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.206301 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.206304 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.206307 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.206309 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.206312 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.206315 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.206318 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.206321 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.206325 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.206328 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.206331 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.206335 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.206341 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.206344 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.206347 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.206350 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.206353 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.206356 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.206361 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.206364 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.206366 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.206369 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.206372 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.206375 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.206378 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.206380 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.206383 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.206386 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.206388 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.206391 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.206394 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.206397 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.206399 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.206402 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.206405 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.206407 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.206410 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.206413 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.206416 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.206419 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.206422 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.206425 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.206427 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.210388 4be8 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/MANIFEST-000005 succeeded,manifest_file_number is 5, next_file_number is 7, last_sequence is 0, log_number is 4,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/06/19-19:27:56.210403 4be8 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:27:56.210407 4be8 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 4
2025/06/19-19:27:56.210834 4be8 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9f-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:27:56.212114 4be8 EVENT_LOG_v1 {"time_micros": 1750332476212106, "job": 1, "event": "recovery_started", "wal_files": [4]}
2025/06/19-19:27:56.212123 4be8 [db\db_impl\db_impl_open.cc:1029] Recovering log #4 mode 2
2025/06/19-19:27:56.214003 4be8 EVENT_LOG_v1 {"time_micros": 1750332476213960, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 10, "file_size": 1117, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 1, "largest_seqno": 1, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQE", "orig_file_number": 10, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.218119 4be8 EVENT_LOG_v1 {"time_micros": 1750332476218079, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 11, "file_size": 1123, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 2, "largest_seqno": 2, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9f-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQE", "orig_file_number": 11, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.221376 4be8 EVENT_LOG_v1 {"time_micros": 1750332476221369, "job": 1, "event": "recovery_finished"}
2025/06/19-19:27:56.221862 4be8 [db\version_set.cc:5051] Creating manifest 13
2025/06/19-19:27:56.230745 4be8 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_service_metadata\log/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-19:27:56.230777 4be8 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 00000282FE410E20
2025/06/19-19:27:56.231346 4be8 DB pointer 00000282FE89E000
2025/06/19-19:28:02.241658 3edc [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-19:28:02.241677 3edc [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 6.0 total, 6.0 interval
Cumulative writes: 2 writes, 3 keys, 2 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 2 writes, 2 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2 writes, 3 keys, 2 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 2 writes, 2 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.09 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.09 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D2ED60A0#9160 capacity: 512.00 MB usage: 0.86 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.26 KB,4.95464e-05%) IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.10 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.10 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 6.0 total, 6.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D2ED60A0#9160 capacity: 512.00 MB usage: 0.86 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.26 KB,4.95464e-05%) IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 3 Average: 9.6667  StdDev: 2.36
Min: 8  Median: 9.0000  Max: 13
Percentiles: P50: 9.00 P75: 11.25 P99: 13.00 P99.9: 13.00 P99.99: 13.00
------------------------------------------------------
(       6,      10 ]        2  66.667%  66.667% #############
(      10,      15 ]        1  33.333% 100.000% #######


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 3 Average: 11.6667  StdDev: 2.62
Min: 8  Median: 11.2500  Max: 14
Percentiles: P50: 11.25 P75: 13.12 P99: 14.00 P99.9: 14.00 P99.99: 14.00
------------------------------------------------------
(       6,      10 ]        1  33.333%  33.333% #######
(      10,      15 ]        2  66.667% 100.000% #############

2025/06/19-19:28:02.242316 3edc [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 4
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 4
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 184
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 2
rocksdb.block.cache.data.hit COUNT : 4
rocksdb.block.cache.data.add COUNT : 2
rocksdb.block.cache.data.bytes.insert COUNT : 266
rocksdb.block.cache.bytes.read COUNT : 532
rocksdb.block.cache.bytes.write COUNT : 450
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 3
rocksdb.l0.hit COUNT : 3
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 3
rocksdb.number.keys.read COUNT : 3
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 145
rocksdb.bytes.read COUNT : 102
rocksdb.number.db.seek COUNT : 3
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 3
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 126
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 2
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 2
rocksdb.wal.bytes COUNT : 145
rocksdb.write.self COUNT : 2
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 2
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2240
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 3
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 3
rocksdb.num.iterator.deleted COUNT : 3
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 4612
rocksdb.non.last.level.read.count COUNT : 6
rocksdb.block.checksum.compute.count COUNT : 8
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 28.000000 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 3 SUM : 81
rocksdb.db.write.micros P50 : 516.000000 P95 : 537.000000 P99 : 537.000000 P100 : 537.000000 COUNT : 2 SUM : 1053
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 580.000000 P95 : 608.000000 P99 : 608.000000 P100 : 608.000000 COUNT : 2 SUM : 1118
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 466.000000 P95 : 466.000000 P99 : 466.000000 P100 : 466.000000 COUNT : 2 SUM : 852
rocksdb.manifest.file.sync.micros P50 : 475.000000 P95 : 475.000000 P99 : 475.000000 P100 : 475.000000 COUNT : 1 SUM : 475
rocksdb.table.open.io.micros P50 : 93.000000 P95 : 94.000000 P99 : 94.000000 P100 : 94.000000 COUNT : 2 SUM : 182
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 1.000000 P95 : 19.000000 P99 : 19.000000 P100 : 19.000000 COUNT : 6 SUM : 33
rocksdb.write.raw.block.micros P50 : 0.714286 P95 : 3.500000 P99 : 3.900000 P100 : 4.000000 COUNT : 10 SUM : 14
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 10.000000 P95 : 14.000000 P99 : 14.000000 P100 : 14.000000 COUNT : 6 SUM : 64
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 34.000000 P95 : 34.000000 P99 : 34.000000 P100 : 34.000000 COUNT : 3 SUM : 102
rocksdb.bytes.per.write P50 : 51.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 2 SUM : 145
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1032.000000 P95 : 1032.000000 P99 : 1032.000000 P100 : 1032.000000 COUNT : 2 SUM : 2058
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-19:28:03.237762 3edc [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-19:28:03.237795 3edc [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-19:28:03.237798 3edc [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
