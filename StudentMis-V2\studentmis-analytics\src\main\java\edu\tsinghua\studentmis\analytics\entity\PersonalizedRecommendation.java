package edu.tsinghua.studentmis.analytics.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 个性化推荐实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("personalized_recommendation")
public class PersonalizedRecommendation {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 推荐类型
     */
    @TableField("recommendation_type")
    private String recommendationType;

    /**
     * 推荐项目ID
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 推荐项目名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 推荐项目类型
     */
    @TableField("item_type")
    private String itemType;

    /**
     * 推荐评分
     */
    @TableField("recommendation_score")
    private BigDecimal recommendationScore;

    /**
     * 匹配度
     */
    @TableField("match_score")
    private BigDecimal matchScore;

    /**
     * 置信度
     */
    @TableField("confidence")
    private BigDecimal confidence;

    /**
     * 推荐算法
     */
    @TableField("algorithm")
    private String algorithm;

    /**
     * 算法版本
     */
    @TableField("algorithm_version")
    private String algorithmVersion;

    /**
     * 推荐原因（JSON格式）
     */
    @TableField("reasons")
    private String reasons;

    /**
     * 推荐标签（JSON格式）
     */
    @TableField("tags")
    private String tags;

    /**
     * 难度等级
     */
    @TableField("difficulty_level")
    private String difficultyLevel;

    /**
     * 预估学习时长（小时）
     */
    @TableField("estimated_hours")
    private Integer estimatedHours;

    /**
     * 推荐描述
     */
    @TableField("description")
    private String description;

    /**
     * 相关资源（JSON格式）
     */
    @TableField("related_resources")
    private String relatedResources;

    /**
     * 推荐详情（JSON格式）
     */
    @TableField("recommendation_details")
    private String recommendationDetails;

    /**
     * 推荐状态
     */
    @TableField("recommendation_status")
    private String recommendationStatus;

    /**
     * 学生反馈
     */
    @TableField("student_feedback")
    private String studentFeedback;

    /**
     * 反馈评分
     */
    @TableField("feedback_score")
    private BigDecimal feedbackScore;

    /**
     * 是否已查看
     */
    @TableField("is_viewed")
    private Boolean isViewed;

    /**
     * 查看时间
     */
    @TableField("viewed_at")
    private LocalDateTime viewedAt;

    /**
     * 是否已采纳
     */
    @TableField("is_adopted")
    private Boolean isAdopted;

    /**
     * 采纳时间
     */
    @TableField("adopted_at")
    private LocalDateTime adoptedAt;

    /**
     * 过期时间
     */
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private Long updatedBy;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 推荐状态枚举
     */
    public enum RecommendationStatus {
        ACTIVE("有效"),
        EXPIRED("已过期"),
        ADOPTED("已采纳"),
        REJECTED("已拒绝"),
        PENDING("待处理");

        private final String description;

        RecommendationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
