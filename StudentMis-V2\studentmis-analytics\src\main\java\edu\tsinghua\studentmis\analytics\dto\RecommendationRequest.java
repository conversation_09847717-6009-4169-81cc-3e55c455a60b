package edu.tsinghua.studentmis.analytics.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 个性化推荐请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "个性化推荐请求")
public class RecommendationRequest {

    @Schema(description = "学生ID", required = true)
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "推荐类型", allowableValues = {"COURSE", "STUDY_PLAN", "LEARNING_RESOURCE", "CAREER_PATH"})
    private String recommendationType;

    @Schema(description = "推荐数量")
    private Integer recommendationCount = 10;

    @Schema(description = "学期ID")
    private Long semesterId;

    @Schema(description = "专业ID")
    private Long majorId;

    @Schema(description = "学生兴趣标签")
    private List<String> interestTags;

    @Schema(description = "学习目标")
    private List<String> learningGoals;

    @Schema(description = "当前成绩水平", allowableValues = {"EXCELLENT", "GOOD", "AVERAGE", "BELOW_AVERAGE"})
    private String currentGradeLevel;

    @Schema(description = "学习风格", allowableValues = {"VISUAL", "AUDITORY", "KINESTHETIC", "READING_WRITING"})
    private String learningStyle;

    @Schema(description = "时间偏好")
    private TimePreference timePreference;

    @Schema(description = "难度偏好", allowableValues = {"EASY", "MEDIUM", "HARD", "ADAPTIVE"})
    private String difficultyPreference = "ADAPTIVE";

    @Schema(description = "排除项目")
    private List<Long> excludeItems;

    @Schema(description = "额外参数")
    private Map<String, Object> additionalParams;

    /**
     * 时间偏好
     */
    @Data
    @Schema(description = "时间偏好")
    public static class TimePreference {
        
        @Schema(description = "偏好时间段")
        private List<String> preferredTimeSlots;

        @Schema(description = "每日学习时长（分钟）")
        private Integer dailyStudyMinutes;

        @Schema(description = "每周学习天数")
        private Integer weeklyStudyDays;

        @Schema(description = "是否偏好集中学习")
        private Boolean preferIntensiveStudy = false;
    }

    /**
     * 推荐类型枚举
     */
    public enum RecommendationType {
        COURSE("课程推荐"),
        STUDY_PLAN("学习计划推荐"),
        LEARNING_RESOURCE("学习资源推荐"),
        CAREER_PATH("职业路径推荐");

        private final String description;

        RecommendationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 成绩水平枚举
     */
    public enum GradeLevel {
        EXCELLENT("优秀"),
        GOOD("良好"),
        AVERAGE("一般"),
        BELOW_AVERAGE("待提高");

        private final String description;

        GradeLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 学习风格枚举
     */
    public enum LearningStyle {
        VISUAL("视觉型"),
        AUDITORY("听觉型"),
        KINESTHETIC("动觉型"),
        READING_WRITING("读写型");

        private final String description;

        LearningStyle(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 难度偏好枚举
     */
    public enum DifficultyPreference {
        EASY("简单"),
        MEDIUM("中等"),
        HARD("困难"),
        ADAPTIVE("自适应");

        private final String description;

        DifficultyPreference(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
