package edu.tsinghua.studentmis.analytics.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 学习行为分析实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("learning_behavior_analysis")
public class LearningBehaviorAnalysis {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 学生ID
     */
    @TableField("student_id")
    private Long studentId;

    /**
     * 学期ID
     */
    @TableField("semester_id")
    private Long semesterId;

    /**
     * 分析类型
     */
    @TableField("analysis_type")
    private String analysisType;

    /**
     * 分析维度
     */
    @TableField("analysis_dimension")
    private String analysisDimension;

    /**
     * 分析开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 分析结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 总体评分
     */
    @TableField("overall_score")
    private BigDecimal overallScore;

    /**
     * 学习活跃度
     */
    @TableField("learning_activity")
    private BigDecimal learningActivity;

    /**
     * 出勤率
     */
    @TableField("attendance_rate")
    private BigDecimal attendanceRate;

    /**
     * 作业完成率
     */
    @TableField("assignment_completion_rate")
    private BigDecimal assignmentCompletionRate;

    /**
     * 平均成绩
     */
    @TableField("average_grade")
    private BigDecimal averageGrade;

    /**
     * 学习时长统计（JSON格式）
     */
    @TableField("study_time_stats")
    private String studyTimeStats;

    /**
     * 行为模式分析（JSON格式）
     */
    @TableField("behavior_patterns")
    private String behaviorPatterns;

    /**
     * 学习建议（JSON格式）
     */
    @TableField("recommendations")
    private String recommendations;

    /**
     * 预测结果（JSON格式）
     */
    @TableField("prediction_result")
    private String predictionResult;

    /**
     * 对比分析结果（JSON格式）
     */
    @TableField("comparison_result")
    private String comparisonResult;

    /**
     * 详细数据（JSON格式）
     */
    @TableField("detail_data")
    private String detailData;

    /**
     * 分析状态
     */
    @TableField("analysis_status")
    private String analysisStatus;

    /**
     * 分析算法版本
     */
    @TableField("algorithm_version")
    private String algorithmVersion;

    /**
     * 置信度
     */
    @TableField("confidence")
    private BigDecimal confidence;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private Long updatedBy;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 分析状态枚举
     */
    public enum AnalysisStatus {
        PENDING("待分析"),
        PROCESSING("分析中"),
        COMPLETED("已完成"),
        FAILED("分析失败");

        private final String description;

        AnalysisStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
