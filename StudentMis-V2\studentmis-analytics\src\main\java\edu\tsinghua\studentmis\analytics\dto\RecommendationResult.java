package edu.tsinghua.studentmis.analytics.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 个性化推荐结果DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Builder
@Schema(description = "个性化推荐结果")
public class RecommendationResult {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "推荐类型")
    private String recommendationType;

    @Schema(description = "推荐时间")
    private LocalDateTime recommendationTime;

    @Schema(description = "推荐项目列表")
    private List<RecommendationItem> recommendations;

    @Schema(description = "推荐算法")
    private String algorithm;

    @Schema(description = "推荐置信度")
    private BigDecimal confidence;

    @Schema(description = "个性化评分")
    private BigDecimal personalizationScore;

    @Schema(description = "推荐原因")
    private List<String> reasons;

    @Schema(description = "相关统计")
    private RecommendationStats stats;

    @Schema(description = "额外信息")
    private Map<String, Object> metadata;

    /**
     * 推荐项目
     */
    @Data
    @Builder
    @Schema(description = "推荐项目")
    public static class RecommendationItem {
        
        @Schema(description = "项目ID")
        private Long itemId;

        @Schema(description = "项目名称")
        private String itemName;

        @Schema(description = "项目类型")
        private String itemType;

        @Schema(description = "推荐评分")
        private BigDecimal score;

        @Schema(description = "匹配度")
        private BigDecimal matchScore;

        @Schema(description = "难度等级")
        private String difficultyLevel;

        @Schema(description = "预估学习时长（小时）")
        private Integer estimatedHours;

        @Schema(description = "推荐理由")
        private List<String> reasons;

        @Schema(description = "标签")
        private List<String> tags;

        @Schema(description = "描述")
        private String description;

        @Schema(description = "相关资源")
        private List<RelatedResource> relatedResources;

        @Schema(description = "项目详情")
        private Map<String, Object> details;
    }

    /**
     * 相关资源
     */
    @Data
    @Builder
    @Schema(description = "相关资源")
    public static class RelatedResource {
        
        @Schema(description = "资源ID")
        private Long resourceId;

        @Schema(description = "资源名称")
        private String resourceName;

        @Schema(description = "资源类型")
        private String resourceType;

        @Schema(description = "资源链接")
        private String resourceUrl;

        @Schema(description = "资源描述")
        private String description;
    }

    /**
     * 推荐统计
     */
    @Data
    @Builder
    @Schema(description = "推荐统计")
    public static class RecommendationStats {
        
        @Schema(description = "总推荐数量")
        private Integer totalRecommendations;

        @Schema(description = "高匹配度推荐数量")
        private Integer highMatchCount;

        @Schema(description = "中匹配度推荐数量")
        private Integer mediumMatchCount;

        @Schema(description = "低匹配度推荐数量")
        private Integer lowMatchCount;

        @Schema(description = "平均匹配度")
        private BigDecimal averageMatchScore;

        @Schema(description = "覆盖的兴趣标签")
        private List<String> coveredInterestTags;

        @Schema(description = "推荐多样性评分")
        private BigDecimal diversityScore;

        @Schema(description = "新颖性评分")
        private BigDecimal noveltyScore;
    }
}
