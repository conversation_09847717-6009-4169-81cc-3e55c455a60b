2025/06/19-20:11:02.421953 37ac RocksDB version: 7.7.3
2025/06/19-20:11:02.422040 37ac Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:11:02.422059 37ac Compile date 2022-10-24 17:17:55
2025/06/19-20:11:02.422072 37ac DB SUMMARY
2025/06/19-20:11:02.422083 37ac DB Session ID:  YAARR2AMVZBTGB1KBVTX
2025/06/19-20:11:02.422984 37ac CURRENT file:  CURRENT
2025/06/19-20:11:02.423001 37ac IDENTITY file:  IDENTITY
2025/06/19-20:11:02.423134 37ac MANIFEST file:  MANIFEST-000013 size: 340 Bytes
2025/06/19-20:11:02.423149 37ac SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log dir, Total Num: 2, files: 000010.sst 000011.sst 
2025/06/19-20:11:02.423162 37ac Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log: 000012.log size: 159 ; 
2025/06/19-20:11:02.423328 37ac                         Options.error_if_exists: 0
2025/06/19-20:11:02.423337 37ac                       Options.create_if_missing: 1
2025/06/19-20:11:02.423340 37ac                         Options.paranoid_checks: 1
2025/06/19-20:11:02.423343 37ac             Options.flush_verify_memtable_count: 1
2025/06/19-20:11:02.423345 37ac                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:11:02.423348 37ac        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:11:02.423350 37ac                                     Options.env: 0000018608B495C0
2025/06/19-20:11:02.423353 37ac                                      Options.fs: WinFS
2025/06/19-20:11:02.423356 37ac                                Options.info_log: 000001860E0561C0
2025/06/19-20:11:02.423358 37ac                Options.max_file_opening_threads: 16
2025/06/19-20:11:02.423361 37ac                              Options.statistics: 0000018609F6F4D0
2025/06/19-20:11:02.423363 37ac                               Options.use_fsync: 0
2025/06/19-20:11:02.423366 37ac                       Options.max_log_file_size: 0
2025/06/19-20:11:02.423368 37ac                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:11:02.423371 37ac                   Options.log_file_time_to_roll: 0
2025/06/19-20:11:02.423373 37ac                       Options.keep_log_file_num: 100
2025/06/19-20:11:02.423376 37ac                    Options.recycle_log_file_num: 0
2025/06/19-20:11:02.423378 37ac                         Options.allow_fallocate: 1
2025/06/19-20:11:02.423381 37ac                        Options.allow_mmap_reads: 0
2025/06/19-20:11:02.423383 37ac                       Options.allow_mmap_writes: 0
2025/06/19-20:11:02.423385 37ac                        Options.use_direct_reads: 0
2025/06/19-20:11:02.423388 37ac                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:11:02.423390 37ac          Options.create_missing_column_families: 1
2025/06/19-20:11:02.423393 37ac                              Options.db_log_dir: 
2025/06/19-20:11:02.423395 37ac                                 Options.wal_dir: 
2025/06/19-20:11:02.423398 37ac                Options.table_cache_numshardbits: 6
2025/06/19-20:11:02.423400 37ac                         Options.WAL_ttl_seconds: 0
2025/06/19-20:11:02.423402 37ac                       Options.WAL_size_limit_MB: 0
2025/06/19-20:11:02.423405 37ac                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:11:02.423407 37ac             Options.manifest_preallocation_size: 4194304
2025/06/19-20:11:02.423410 37ac                     Options.is_fd_close_on_exec: 1
2025/06/19-20:11:02.423412 37ac                   Options.advise_random_on_open: 1
2025/06/19-20:11:02.423415 37ac                    Options.db_write_buffer_size: 0
2025/06/19-20:11:02.423417 37ac                    Options.write_buffer_manager: 0000018608B476D0
2025/06/19-20:11:02.423420 37ac         Options.access_hint_on_compaction_start: 1
2025/06/19-20:11:02.423422 37ac           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:11:02.423425 37ac                      Options.use_adaptive_mutex: 0
2025/06/19-20:11:02.423456 37ac                            Options.rate_limiter: 0000000000000000
2025/06/19-20:11:02.423461 37ac     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:11:02.423463 37ac                       Options.wal_recovery_mode: 2
2025/06/19-20:11:02.423466 37ac                  Options.enable_thread_tracking: 0
2025/06/19-20:11:02.423468 37ac                  Options.enable_pipelined_write: 0
2025/06/19-20:11:02.423471 37ac                  Options.unordered_write: 0
2025/06/19-20:11:02.423473 37ac         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:11:02.423476 37ac      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:11:02.423478 37ac             Options.write_thread_max_yield_usec: 100
2025/06/19-20:11:02.423481 37ac            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:11:02.423483 37ac                               Options.row_cache: None
2025/06/19-20:11:02.423486 37ac                              Options.wal_filter: None
2025/06/19-20:11:02.423488 37ac             Options.avoid_flush_during_recovery: 0
2025/06/19-20:11:02.423491 37ac             Options.allow_ingest_behind: 0
2025/06/19-20:11:02.423493 37ac             Options.two_write_queues: 0
2025/06/19-20:11:02.423496 37ac             Options.manual_wal_flush: 0
2025/06/19-20:11:02.423499 37ac             Options.wal_compression: 0
2025/06/19-20:11:02.423501 37ac             Options.atomic_flush: 0
2025/06/19-20:11:02.423503 37ac             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:11:02.423506 37ac                 Options.persist_stats_to_disk: 0
2025/06/19-20:11:02.423509 37ac                 Options.write_dbid_to_manifest: 0
2025/06/19-20:11:02.423511 37ac                 Options.log_readahead_size: 0
2025/06/19-20:11:02.423513 37ac                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:11:02.423516 37ac                 Options.best_efforts_recovery: 0
2025/06/19-20:11:02.423520 37ac                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:11:02.423523 37ac            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:11:02.423531 37ac             Options.allow_data_in_errors: 0
2025/06/19-20:11:02.423553 37ac             Options.db_host_id: __hostname__
2025/06/19-20:11:02.423562 37ac             Options.enforce_single_del_contracts: true
2025/06/19-20:11:02.423565 37ac             Options.max_background_jobs: 2
2025/06/19-20:11:02.423568 37ac             Options.max_background_compactions: 4
2025/06/19-20:11:02.423570 37ac             Options.max_subcompactions: 1
2025/06/19-20:11:02.423573 37ac             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:11:02.423575 37ac           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:11:02.423578 37ac             Options.delayed_write_rate : 16777216
2025/06/19-20:11:02.423581 37ac             Options.max_total_wal_size: 1073741824
2025/06/19-20:11:02.423583 37ac             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:11:02.423586 37ac                   Options.stats_dump_period_sec: 600
2025/06/19-20:11:02.423588 37ac                 Options.stats_persist_period_sec: 600
2025/06/19-20:11:02.423591 37ac                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:11:02.423593 37ac                          Options.max_open_files: -1
2025/06/19-20:11:02.423596 37ac                          Options.bytes_per_sync: 0
2025/06/19-20:11:02.423599 37ac                      Options.wal_bytes_per_sync: 0
2025/06/19-20:11:02.423601 37ac                   Options.strict_bytes_per_sync: 0
2025/06/19-20:11:02.423604 37ac       Options.compaction_readahead_size: 0
2025/06/19-20:11:02.423606 37ac                  Options.max_background_flushes: 1
2025/06/19-20:11:02.423609 37ac Compression algorithms supported:
2025/06/19-20:11:02.423612 37ac 	kZSTD supported: 1
2025/06/19-20:11:02.423615 37ac 	kSnappyCompression supported: 1
2025/06/19-20:11:02.423617 37ac 	kBZip2Compression supported: 0
2025/06/19-20:11:02.423620 37ac 	kZlibCompression supported: 1
2025/06/19-20:11:02.423661 37ac 	kLZ4Compression supported: 1
2025/06/19-20:11:02.423666 37ac 	kXpressCompression supported: 0
2025/06/19-20:11:02.423668 37ac 	kLZ4HCCompression supported: 1
2025/06/19-20:11:02.423671 37ac 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:11:02.423675 37ac Fast CRC32 supported: Not supported on x86
2025/06/19-20:11:02.423678 37ac DMutex implementation: std::mutex
2025/06/19-20:11:02.425044 37ac [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000013
2025/06/19-20:11:02.425322 37ac [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:11:02.425336 37ac               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:11:02.425340 37ac           Options.merge_operator: StringAppendOperator
2025/06/19-20:11:02.425343 37ac        Options.compaction_filter: None
2025/06/19-20:11:02.425346 37ac        Options.compaction_filter_factory: None
2025/06/19-20:11:02.425349 37ac  Options.sst_partitioner_factory: None
2025/06/19-20:11:02.425352 37ac         Options.memtable_factory: SkipListFactory
2025/06/19-20:11:02.425355 37ac            Options.table_factory: BlockBasedTable
2025/06/19-20:11:02.425393 37ac            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001860DFF3020)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018609F6C480
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:11:02.425398 37ac        Options.write_buffer_size: 67108864
2025/06/19-20:11:02.425400 37ac  Options.max_write_buffer_number: 3
2025/06/19-20:11:02.425403 37ac          Options.compression: Snappy
2025/06/19-20:11:02.425406 37ac                  Options.bottommost_compression: Disabled
2025/06/19-20:11:02.425409 37ac       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:11:02.425412 37ac   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:11:02.425414 37ac             Options.num_levels: 7
2025/06/19-20:11:02.425417 37ac        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:11:02.425419 37ac     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:11:02.425421 37ac     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:11:02.425424 37ac            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:11:02.425427 37ac                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:11:02.425429 37ac               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:11:02.425432 37ac         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.425434 37ac         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.425437 37ac         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:11:02.425439 37ac                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:11:02.425448 37ac         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.425451 37ac         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.425454 37ac            Options.compression_opts.window_bits: -14
2025/06/19-20:11:02.425456 37ac                  Options.compression_opts.level: 32767
2025/06/19-20:11:02.425459 37ac               Options.compression_opts.strategy: 0
2025/06/19-20:11:02.425461 37ac         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.425464 37ac         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.425466 37ac         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.425469 37ac         Options.compression_opts.parallel_threads: 1
2025/06/19-20:11:02.425471 37ac                  Options.compression_opts.enabled: false
2025/06/19-20:11:02.425474 37ac         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.425476 37ac      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:11:02.425479 37ac          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:11:02.425481 37ac              Options.level0_stop_writes_trigger: 40
2025/06/19-20:11:02.425484 37ac                   Options.target_file_size_base: 67108864
2025/06/19-20:11:02.425486 37ac             Options.target_file_size_multiplier: 1
2025/06/19-20:11:02.425489 37ac                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:11:02.425491 37ac Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:11:02.425494 37ac          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:11:02.425497 37ac Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:11:02.425499 37ac Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:11:02.425502 37ac Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:11:02.425504 37ac Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:11:02.425507 37ac Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:11:02.425510 37ac Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:11:02.425512 37ac Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:11:02.425514 37ac       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:11:02.425517 37ac                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:11:02.425519 37ac                        Options.arena_block_size: 1048576
2025/06/19-20:11:02.425522 37ac   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:11:02.425524 37ac   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:11:02.425527 37ac                Options.disable_auto_compactions: 0
2025/06/19-20:11:02.425531 37ac                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:11:02.425534 37ac                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:11:02.425537 37ac Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:11:02.425539 37ac Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:11:02.425542 37ac Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:11:02.425544 37ac Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:11:02.425547 37ac Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:11:02.425552 37ac Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:11:02.425556 37ac Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:11:02.425558 37ac Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:11:02.425571 37ac                   Options.table_properties_collectors: 
2025/06/19-20:11:02.425573 37ac                   Options.inplace_update_support: 0
2025/06/19-20:11:02.425576 37ac                 Options.inplace_update_num_locks: 10000
2025/06/19-20:11:02.425578 37ac               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:11:02.425631 37ac               Options.memtable_whole_key_filtering: 0
2025/06/19-20:11:02.425636 37ac   Options.memtable_huge_page_size: 0
2025/06/19-20:11:02.425638 37ac                           Options.bloom_locality: 0
2025/06/19-20:11:02.425641 37ac                    Options.max_successive_merges: 0
2025/06/19-20:11:02.425643 37ac                Options.optimize_filters_for_hits: 0
2025/06/19-20:11:02.425646 37ac                Options.paranoid_file_checks: 0
2025/06/19-20:11:02.425648 37ac                Options.force_consistency_checks: 1
2025/06/19-20:11:02.425651 37ac                Options.report_bg_io_stats: 0
2025/06/19-20:11:02.425654 37ac                               Options.ttl: 2592000
2025/06/19-20:11:02.425657 37ac          Options.periodic_compaction_seconds: 0
2025/06/19-20:11:02.425660 37ac  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:11:02.425662 37ac                       Options.enable_blob_files: false
2025/06/19-20:11:02.425665 37ac                           Options.min_blob_size: 0
2025/06/19-20:11:02.425667 37ac                          Options.blob_file_size: 268435456
2025/06/19-20:11:02.425670 37ac                   Options.blob_compression_type: NoCompression
2025/06/19-20:11:02.425673 37ac          Options.enable_blob_garbage_collection: false
2025/06/19-20:11:02.425675 37ac      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:11:02.425678 37ac Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:11:02.425681 37ac          Options.blob_compaction_readahead_size: 0
2025/06/19-20:11:02.425684 37ac                Options.blob_file_starting_level: 0
2025/06/19-20:11:02.425686 37ac Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:11:02.427290 37ac [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:11:02.427307 37ac               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:11:02.427311 37ac           Options.merge_operator: StringAppendOperator
2025/06/19-20:11:02.427315 37ac        Options.compaction_filter: None
2025/06/19-20:11:02.427318 37ac        Options.compaction_filter_factory: None
2025/06/19-20:11:02.427322 37ac  Options.sst_partitioner_factory: None
2025/06/19-20:11:02.427325 37ac         Options.memtable_factory: SkipListFactory
2025/06/19-20:11:02.427327 37ac            Options.table_factory: BlockBasedTable
2025/06/19-20:11:02.427354 37ac            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (000001860DFF3020)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018609F6C480
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:11:02.427358 37ac        Options.write_buffer_size: 67108864
2025/06/19-20:11:02.427360 37ac  Options.max_write_buffer_number: 3
2025/06/19-20:11:02.427363 37ac          Options.compression: Snappy
2025/06/19-20:11:02.427365 37ac                  Options.bottommost_compression: Disabled
2025/06/19-20:11:02.427368 37ac       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:11:02.427373 37ac   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:11:02.427377 37ac             Options.num_levels: 7
2025/06/19-20:11:02.427379 37ac        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:11:02.427382 37ac     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:11:02.427384 37ac     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:11:02.427386 37ac            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:11:02.427389 37ac                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:11:02.427391 37ac               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:11:02.427394 37ac         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.427396 37ac         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.427399 37ac         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:11:02.427401 37ac                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:11:02.427404 37ac         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.427406 37ac         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.427409 37ac            Options.compression_opts.window_bits: -14
2025/06/19-20:11:02.427411 37ac                  Options.compression_opts.level: 32767
2025/06/19-20:11:02.427414 37ac               Options.compression_opts.strategy: 0
2025/06/19-20:11:02.427416 37ac         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.427419 37ac         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.427421 37ac         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.427423 37ac         Options.compression_opts.parallel_threads: 1
2025/06/19-20:11:02.427426 37ac                  Options.compression_opts.enabled: false
2025/06/19-20:11:02.427428 37ac         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.427431 37ac      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:11:02.427433 37ac          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:11:02.427436 37ac              Options.level0_stop_writes_trigger: 40
2025/06/19-20:11:02.427438 37ac                   Options.target_file_size_base: 67108864
2025/06/19-20:11:02.427440 37ac             Options.target_file_size_multiplier: 1
2025/06/19-20:11:02.427443 37ac                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:11:02.427445 37ac Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:11:02.427448 37ac          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:11:02.427451 37ac Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:11:02.427453 37ac Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:11:02.427455 37ac Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:11:02.427458 37ac Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:11:02.427460 37ac Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:11:02.427463 37ac Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:11:02.427465 37ac Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:11:02.427468 37ac       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:11:02.427470 37ac                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:11:02.427473 37ac                        Options.arena_block_size: 1048576
2025/06/19-20:11:02.427475 37ac   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:11:02.427478 37ac   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:11:02.427480 37ac                Options.disable_auto_compactions: 0
2025/06/19-20:11:02.427483 37ac                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:11:02.427487 37ac                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:11:02.427490 37ac Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:11:02.427492 37ac Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:11:02.427495 37ac Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:11:02.427498 37ac Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:11:02.427500 37ac Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:11:02.427503 37ac Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:11:02.427506 37ac Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:11:02.427509 37ac Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:11:02.427514 37ac                   Options.table_properties_collectors: 
2025/06/19-20:11:02.427516 37ac                   Options.inplace_update_support: 0
2025/06/19-20:11:02.427519 37ac                 Options.inplace_update_num_locks: 10000
2025/06/19-20:11:02.427521 37ac               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:11:02.427524 37ac               Options.memtable_whole_key_filtering: 0
2025/06/19-20:11:02.427526 37ac   Options.memtable_huge_page_size: 0
2025/06/19-20:11:02.427529 37ac                           Options.bloom_locality: 0
2025/06/19-20:11:02.427531 37ac                    Options.max_successive_merges: 0
2025/06/19-20:11:02.427534 37ac                Options.optimize_filters_for_hits: 0
2025/06/19-20:11:02.427536 37ac                Options.paranoid_file_checks: 0
2025/06/19-20:11:02.427539 37ac                Options.force_consistency_checks: 1
2025/06/19-20:11:02.427541 37ac                Options.report_bg_io_stats: 0
2025/06/19-20:11:02.427543 37ac                               Options.ttl: 2592000
2025/06/19-20:11:02.427546 37ac          Options.periodic_compaction_seconds: 0
2025/06/19-20:11:02.427548 37ac  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:11:02.427551 37ac                       Options.enable_blob_files: false
2025/06/19-20:11:02.427553 37ac                           Options.min_blob_size: 0
2025/06/19-20:11:02.427556 37ac                          Options.blob_file_size: 268435456
2025/06/19-20:11:02.427558 37ac                   Options.blob_compression_type: NoCompression
2025/06/19-20:11:02.427561 37ac          Options.enable_blob_garbage_collection: false
2025/06/19-20:11:02.427563 37ac      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:11:02.427566 37ac Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:11:02.427569 37ac          Options.blob_compaction_readahead_size: 0
2025/06/19-20:11:02.427571 37ac                Options.blob_file_starting_level: 0
2025/06/19-20:11:02.427574 37ac Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:11:02.430851 37ac [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000013 succeeded,manifest_file_number is 13, next_file_number is 15, last_sequence is 2, log_number is 5,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 5
2025/06/19-20:11:02.430866 37ac [db\version_set.cc:5588] Column family [default] (ID 0), log number is 5
2025/06/19-20:11:02.430869 37ac [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 5
2025/06/19-20:11:02.431286 37ac [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9e-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:11:02.433198 37ac EVENT_LOG_v1 {"time_micros": 1750335062433189, "job": 1, "event": "recovery_started", "wal_files": [12]}
2025/06/19-20:11:02.433210 37ac [db\db_impl\db_impl_open.cc:1029] Recovering log #12 mode 2
2025/06/19-20:11:02.435029 37ac EVENT_LOG_v1 {"time_micros": 1750335062434994, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 16, "file_size": 1117, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 4, "largest_seqno": 4, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335062, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "YAARR2AMVZBTGB1KBVTX", "orig_file_number": 16, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:11:02.438515 37ac EVENT_LOG_v1 {"time_micros": 1750335062438461, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 17, "file_size": 1181, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 3, "largest_seqno": 5, "table_properties": {"data_size": 103, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335062, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "YAARR2AMVZBTGB1KBVTX", "orig_file_number": 17, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:11:02.441920 37ac EVENT_LOG_v1 {"time_micros": 1750335062441912, "job": 1, "event": "recovery_finished"}
2025/06/19-20:11:02.442394 37ac [db\version_set.cc:5051] Creating manifest 19
2025/06/19-20:11:02.452846 37ac [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/000012.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:11:02.452896 37ac [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 0000018608AD0730
2025/06/19-20:11:02.453698 37ac DB pointer 0000018607B65500
2025/06/19-20:11:05.456352 5be8 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:11:05.456375 5be8 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 4 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 1 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.18 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    2.18 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018609F6C480#21528 capacity: 512.00 MB usage: 1.69 KB table_size: 4096 occupancy: 9 collections: 1 last_copies: 1 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.56 KB,0.000105985%) IndexBlock(4,0.37 KB,7.0408e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.25 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    2.25 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018609F6C480#21528 capacity: 512.00 MB usage: 1.69 KB table_size: 4096 occupancy: 9 collections: 1 last_copies: 1 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(4,0.56 KB,0.000105985%) IndexBlock(4,0.37 KB,7.0408e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 6 Average: 18.5000  StdDev: 11.87
Min: 7  Median: 15.0000  Max: 42
Percentiles: P50: 15.00 P75: 20.25 P99: 42.00 P99.9: 42.00 P99.99: 42.00
------------------------------------------------------
(       6,      10 ]        2  33.333%  33.333% #######
(      10,      15 ]        1  16.667%  50.000% ###
(      15,      22 ]        2  33.333%  83.333% #######
(      34,      51 ]        1  16.667% 100.000% ###


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 6 Average: 18.5000  StdDev: 11.94
Min: 6  Median: 12.5000  Max: 36
Percentiles: P50: 12.50 P75: 28.00 P99: 36.00 P99.9: 36.00 P99.99: 36.00
------------------------------------------------------
(       4,       6 ]        1  16.667%  16.667% ###
(       6,      10 ]        1  16.667%  33.333% ###
(      10,      15 ]        2  33.333%  66.667% #######
(      22,      34 ]        1  16.667%  83.333% ###
(      34,      51 ]        1  16.667% 100.000% ###

2025/06/19-20:11:05.457071 5be8 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 8
rocksdb.block.cache.hit COUNT : 5
rocksdb.block.cache.add COUNT : 8
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 378
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 4
rocksdb.block.cache.data.hit COUNT : 5
rocksdb.block.cache.data.add COUNT : 4
rocksdb.block.cache.data.bytes.insert COUNT : 569
rocksdb.block.cache.bytes.read COUNT : 665
rocksdb.block.cache.bytes.write COUNT : 947
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 4
rocksdb.l0.hit COUNT : 4
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 4
rocksdb.number.keys.read COUNT : 4
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 166
rocksdb.bytes.read COUNT : 136
rocksdb.number.db.seek COUNT : 2
rocksdb.number.db.next COUNT : 3
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 2
rocksdb.number.db.next.found COUNT : 2
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 152
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 4
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 1
rocksdb.wal.bytes COUNT : 166
rocksdb.write.self COUNT : 3
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 3
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2298
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 3
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 2
rocksdb.num.iterator.deleted COUNT : 2
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 9377
rocksdb.non.last.level.read.count COUNT : 12
rocksdb.block.checksum.compute.count COUNT : 16
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 10.000000 P95 : 39.000000 P99 : 39.000000 P100 : 39.000000 COUNT : 4 SUM : 88
rocksdb.db.write.micros P50 : 210.000000 P95 : 601.000000 P99 : 601.000000 P100 : 601.000000 COUNT : 3 SUM : 821
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 580.000000 P95 : 584.000000 P99 : 584.000000 P100 : 584.000000 COUNT : 2 SUM : 1126
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 508.000000 P95 : 508.000000 P99 : 508.000000 P100 : 508.000000 COUNT : 1 SUM : 508
rocksdb.manifest.file.sync.micros P50 : 326.000000 P95 : 326.000000 P99 : 326.000000 P100 : 326.000000 COUNT : 1 SUM : 326
rocksdb.table.open.io.micros P50 : 130.000000 P95 : 159.000000 P99 : 159.000000 P100 : 159.000000 COUNT : 4 SUM : 504
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.857143 P95 : 26.000000 P99 : 26.000000 P100 : 26.000000 COUNT : 12 SUM : 68
rocksdb.write.raw.block.micros P50 : 0.833333 P95 : 3.500000 P99 : 3.900000 P100 : 4.000000 COUNT : 10 SUM : 13
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 13.333333 P95 : 42.000000 P99 : 42.000000 P100 : 42.000000 COUNT : 12 SUM : 222
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 34.000000 P95 : 34.000000 P99 : 34.000000 P100 : 34.000000 COUNT : 4 SUM : 136
rocksdb.bytes.per.write P50 : 31.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 3 SUM : 166
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1043.000000 P95 : 1043.000000 P99 : 1043.000000 P100 : 1043.000000 COUNT : 4 SUM : 4127
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
