package edu.tsinghua.studentmis.analytics.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import edu.tsinghua.studentmis.analytics.dto.RecommendationRequest;
import edu.tsinghua.studentmis.analytics.dto.RecommendationResult;
import edu.tsinghua.studentmis.analytics.entity.PersonalizedRecommendation;
import edu.tsinghua.studentmis.analytics.mapper.PersonalizedRecommendationMapper;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.linear.RealMatrix;
import org.apache.commons.math3.linear.Array2DRowRealMatrix;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 个性化推荐服务
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonalizedRecommendationService {

    private final PersonalizedRecommendationMapper recommendationMapper;
    private final LearningBehaviorAnalysisService behaviorAnalysisService;

    /**
     * 生成个性化推荐
     */
    public List<RecommendationResult> generateRecommendations(RecommendationRequest request) {
        try {
            log.info("开始生成个性化推荐: studentId={}, type={}", 
                    request.getStudentId(), request.getRecommendationType());

            List<RecommendationResult> recommendations = new ArrayList<>();

            switch (request.getRecommendationType()) {
                case "COURSE":
                    recommendations = recommendCourses(request);
                    break;
                case "STUDY_METHOD":
                    recommendations = recommendStudyMethods(request);
                    break;
                case "RESOURCE":
                    recommendations = recommendResources(request);
                    break;
                case "ACTIVITY":
                    recommendations = recommendActivities(request);
                    break;
                default:
                    throw new BusinessException(ResultCode.BAD_REQUEST, "不支持的推荐类型");
            }

            // 保存推荐结果
            saveRecommendations(request.getStudentId(), recommendations);

            log.info("个性化推荐生成完成: studentId={}, count={}", 
                    request.getStudentId(), recommendations.size());

            return recommendations;

        } catch (Exception e) {
            log.error("个性化推荐生成失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.RECOMMENDATION_ERROR, "个性化推荐生成失败: " + e.getMessage());
        }
    }

    /**
     * 推荐课程
     */
    private List<RecommendationResult> recommendCourses(RecommendationRequest request) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        // 获取学生的学习历史和偏好
        Map<String, Object> studentProfile = getStudentProfile(request.getStudentId());
        
        // 获取可选课程
        List<Map<String, Object>> availableCourses = recommendationMapper.getAvailableCourses(request.getStudentId());

        if (CollUtil.isEmpty(availableCourses)) {
            return recommendations;
        }

        // 基于协同过滤推荐课程
        List<RecommendationResult> collaborativeRecommendations = 
                collaborativeFilteringCourseRecommendation(request.getStudentId(), availableCourses);
        recommendations.addAll(collaborativeRecommendations);

        // 基于内容的推荐
        List<RecommendationResult> contentBasedRecommendations = 
                contentBasedCourseRecommendation(studentProfile, availableCourses);
        recommendations.addAll(contentBasedRecommendations);

        // 基于学习能力的推荐
        List<RecommendationResult> abilityBasedRecommendations = 
                abilityBasedCourseRecommendation(studentProfile, availableCourses);
        recommendations.addAll(abilityBasedRecommendations);

        // 去重并排序
        return deduplicateAndSort(recommendations, 5);
    }

    /**
     * 推荐学习方法
     */
    private List<RecommendationResult> recommendStudyMethods(RecommendationRequest request) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        // 获取学生的学习行为分析结果
        var behaviorAnalysis = behaviorAnalysisService.getLatestBehaviorAnalysis(request.getStudentId());
        
        if (behaviorAnalysis == null) {
            return getDefaultStudyMethodRecommendations();
        }

        // 基于学习模式推荐
        String learningPattern = behaviorAnalysis.getLearningPattern();
        recommendations.addAll(getStudyMethodsByPattern(learningPattern));

        // 基于风险等级推荐
        String riskLevel = behaviorAnalysis.getRiskLevel();
        recommendations.addAll(getStudyMethodsByRiskLevel(riskLevel));

        // 基于参与度推荐
        String engagementLevel = behaviorAnalysis.getEngagementLevel();
        recommendations.addAll(getStudyMethodsByEngagement(engagementLevel));

        return deduplicateAndSort(recommendations, 3);
    }

    /**
     * 推荐学习资源
     */
    private List<RecommendationResult> recommendResources(RecommendationRequest request) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        // 获取学生当前课程
        List<Map<String, Object>> currentCourses = recommendationMapper.getCurrentCourses(request.getStudentId());

        for (Map<String, Object> course : currentCourses) {
            Long courseId = (Long) course.get("course_id");
            String courseName = (String) course.get("course_name");

            // 获取课程相关资源
            List<Map<String, Object>> courseResources = recommendationMapper.getCourseResources(courseId);

            // 基于学习进度推荐资源
            List<RecommendationResult> progressBasedResources = 
                    getResourcesByProgress(request.getStudentId(), courseId, courseResources);
            recommendations.addAll(progressBasedResources);

            // 基于成绩表现推荐资源
            List<RecommendationResult> performanceBasedResources = 
                    getResourcesByPerformance(request.getStudentId(), courseId, courseResources);
            recommendations.addAll(performanceBasedResources);
        }

        return deduplicateAndSort(recommendations, 8);
    }

    /**
     * 推荐活动
     */
    private List<RecommendationResult> recommendActivities(RecommendationRequest request) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        // 获取学生兴趣和特长
        Map<String, Object> studentInterests = getStudentInterests(request.getStudentId());

        // 获取可参与活动
        List<Map<String, Object>> availableActivities = recommendationMapper.getAvailableActivities();

        // 基于兴趣匹配推荐活动
        for (Map<String, Object> activity : availableActivities) {
            double matchScore = calculateActivityMatchScore(studentInterests, activity);
            
            if (matchScore > 0.6) {
                RecommendationResult recommendation = RecommendationResult.builder()
                        .recommendationType("ACTIVITY")
                        .itemId((Long) activity.get("id"))
                        .itemName((String) activity.get("name"))
                        .recommendationScore(BigDecimal.valueOf(matchScore * 100).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .reason(generateActivityRecommendationReason(studentInterests, activity))
                        .algorithmUsed("InterestMatching-v1.0")
                        .build();
                
                recommendations.add(recommendation);
            }
        }

        return recommendations.stream()
                .sorted((r1, r2) -> r2.getRecommendationScore().compareTo(r1.getRecommendationScore()))
                .limit(5)
                .collect(Collectors.toList());
    }

    /**
     * 协同过滤课程推荐
     */
    private List<RecommendationResult> collaborativeFilteringCourseRecommendation(Long studentId, 
                                                                                 List<Map<String, Object>> availableCourses) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        try {
            // 获取相似学生
            List<Long> similarStudents = findSimilarStudents(studentId);

            if (CollUtil.isEmpty(similarStudents)) {
                return recommendations;
            }

            // 获取相似学生选择的课程
            Map<Long, Double> courseScores = new HashMap<>();
            
            for (Long similarStudentId : similarStudents) {
                List<Map<String, Object>> similarStudentCourses = 
                        recommendationMapper.getStudentCourseHistory(similarStudentId);
                
                for (Map<String, Object> course : similarStudentCourses) {
                    Long courseId = (Long) course.get("course_id");
                    Double score = ((Number) course.getOrDefault("score", 0)).doubleValue();
                    
                    courseScores.merge(courseId, score, Double::sum);
                }
            }

            // 生成推荐
            for (Map<String, Object> course : availableCourses) {
                Long courseId = (Long) course.get("id");
                String courseName = (String) course.get("name");
                
                if (courseScores.containsKey(courseId)) {
                    double avgScore = courseScores.get(courseId) / similarStudents.size();
                    
                    if (avgScore > 75) { // 只推荐高分课程
                        RecommendationResult recommendation = RecommendationResult.builder()
                                .recommendationType("COURSE")
                                .itemId(courseId)
                                .itemName(courseName)
                                .recommendationScore(BigDecimal.valueOf(avgScore).setScale(2, BigDecimal.ROUND_HALF_UP))
                                .reason("与您学习背景相似的同学在该课程中表现优秀")
                                .algorithmUsed("CollaborativeFiltering-v1.0")
                                .build();
                        
                        recommendations.add(recommendation);
                    }
                }
            }

        } catch (Exception e) {
            log.warn("协同过滤推荐失败: {}", e.getMessage());
        }

        return recommendations;
    }

    /**
     * 基于内容的课程推荐
     */
    private List<RecommendationResult> contentBasedCourseRecommendation(Map<String, Object> studentProfile, 
                                                                       List<Map<String, Object>> availableCourses) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        // 获取学生已学课程的特征
        List<String> learnedCourseCategories = (List<String>) studentProfile.get("courseCategories");
        List<String> preferredDifficulties = (List<String>) studentProfile.get("preferredDifficulties");

        for (Map<String, Object> course : availableCourses) {
            String category = (String) course.get("category");
            String difficulty = (String) course.get("difficulty");
            String courseName = (String) course.get("name");
            Long courseId = (Long) course.get("id");

            double contentScore = 0.0;

            // 类别匹配
            if (learnedCourseCategories != null && learnedCourseCategories.contains(category)) {
                contentScore += 0.6;
            }

            // 难度匹配
            if (preferredDifficulties != null && preferredDifficulties.contains(difficulty)) {
                contentScore += 0.4;
            }

            if (contentScore > 0.5) {
                RecommendationResult recommendation = RecommendationResult.builder()
                        .recommendationType("COURSE")
                        .itemId(courseId)
                        .itemName(courseName)
                        .recommendationScore(BigDecimal.valueOf(contentScore * 100).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .reason(String.format("该课程属于您擅长的%s领域", category))
                        .algorithmUsed("ContentBased-v1.0")
                        .build();
                
                recommendations.add(recommendation);
            }
        }

        return recommendations;
    }

    /**
     * 基于学习能力的课程推荐
     */
    private List<RecommendationResult> abilityBasedCourseRecommendation(Map<String, Object> studentProfile, 
                                                                       List<Map<String, Object>> availableCourses) {
        List<RecommendationResult> recommendations = new ArrayList<>();

        Double avgGPA = (Double) studentProfile.get("avgGPA");
        if (avgGPA == null) {
            return recommendations;
        }

        for (Map<String, Object> course : availableCourses) {
            String difficulty = (String) course.get("difficulty");
            String courseName = (String) course.get("name");
            Long courseId = (Long) course.get("id");

            boolean isRecommended = false;
            String reason = "";

            if (avgGPA >= 3.5) {
                // 高GPA学生推荐有挑战性的课程
                if ("HARD".equals(difficulty)) {
                    isRecommended = true;
                    reason = "基于您的优秀学业表现，推荐具有挑战性的课程";
                }
            } else if (avgGPA >= 2.5) {
                // 中等GPA学生推荐中等难度课程
                if ("MEDIUM".equals(difficulty)) {
                    isRecommended = true;
                    reason = "该课程难度适中，适合您当前的学习水平";
                }
            } else {
                // 低GPA学生推荐基础课程
                if ("EASY".equals(difficulty)) {
                    isRecommended = true;
                    reason = "建议从基础课程开始，逐步提升学习能力";
                }
            }

            if (isRecommended) {
                double score = avgGPA * 25; // 转换为百分制
                RecommendationResult recommendation = RecommendationResult.builder()
                        .recommendationType("COURSE")
                        .itemId(courseId)
                        .itemName(courseName)
                        .recommendationScore(BigDecimal.valueOf(score).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .reason(reason)
                        .algorithmUsed("AbilityBased-v1.0")
                        .build();
                
                recommendations.add(recommendation);
            }
        }

        return recommendations;
    }

    /**
     * 查找相似学生
     */
    private List<Long> findSimilarStudents(Long studentId) {
        // 获取学生特征向量
        Map<String, Object> studentFeatures = getStudentFeatures(studentId);
        
        // 获取所有学生的特征向量
        List<Map<String, Object>> allStudentFeatures = recommendationMapper.getAllStudentFeatures();
        
        // 计算相似度
        List<Map.Entry<Long, Double>> similarities = new ArrayList<>();
        
        for (Map<String, Object> otherFeatures : allStudentFeatures) {
            Long otherStudentId = (Long) otherFeatures.get("student_id");
            if (!otherStudentId.equals(studentId)) {
                double similarity = calculateCosineSimilarity(studentFeatures, otherFeatures);
                similarities.add(new AbstractMap.SimpleEntry<>(otherStudentId, similarity));
            }
        }
        
        // 返回最相似的前10个学生
        return similarities.stream()
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                .limit(10)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(Map<String, Object> features1, Map<String, Object> features2) {
        // 简化实现，实际应该基于特征向量计算
        double similarity = RandomUtil.randomDouble(0.5, 0.9);
        return similarity;
    }

    // 其他辅助方法的简化实现...
    private Map<String, Object> getStudentProfile(Long studentId) {
        return recommendationMapper.getStudentProfile(studentId);
    }

    private Map<String, Object> getStudentFeatures(Long studentId) {
        return recommendationMapper.getStudentFeatures(studentId);
    }

    private Map<String, Object> getStudentInterests(Long studentId) {
        return recommendationMapper.getStudentInterests(studentId);
    }

    private List<RecommendationResult> getDefaultStudyMethodRecommendations() {
        // 返回默认学习方法推荐
        return Arrays.asList(
                RecommendationResult.builder()
                        .recommendationType("STUDY_METHOD")
                        .itemName("制定学习计划")
                        .recommendationScore(BigDecimal.valueOf(85))
                        .reason("良好的学习计划有助于提高学习效率")
                        .algorithmUsed("Default-v1.0")
                        .build()
        );
    }

    private List<RecommendationResult> getStudyMethodsByPattern(String pattern) {
        // 根据学习模式返回推荐
        return new ArrayList<>();
    }

    private List<RecommendationResult> getStudyMethodsByRiskLevel(String riskLevel) {
        // 根据风险等级返回推荐
        return new ArrayList<>();
    }

    private List<RecommendationResult> getStudyMethodsByEngagement(String engagementLevel) {
        // 根据参与度返回推荐
        return new ArrayList<>();
    }

    private List<RecommendationResult> getResourcesByProgress(Long studentId, Long courseId, 
                                                             List<Map<String, Object>> resources) {
        // 根据学习进度推荐资源
        return new ArrayList<>();
    }

    private List<RecommendationResult> getResourcesByPerformance(Long studentId, Long courseId, 
                                                                List<Map<String, Object>> resources) {
        // 根据成绩表现推荐资源
        return new ArrayList<>();
    }

    private double calculateActivityMatchScore(Map<String, Object> interests, Map<String, Object> activity) {
        // 计算活动匹配分数
        return RandomUtil.randomDouble(0.3, 0.9);
    }

    private String generateActivityRecommendationReason(Map<String, Object> interests, Map<String, Object> activity) {
        return "该活动符合您的兴趣爱好";
    }

    private List<RecommendationResult> deduplicateAndSort(List<RecommendationResult> recommendations, int limit) {
        return recommendations.stream()
                .collect(Collectors.toMap(
                        r -> r.getItemId() != null ? r.getItemId() : r.getItemName().hashCode(),
                        r -> r,
                        (existing, replacement) -> existing.getRecommendationScore().compareTo(replacement.getRecommendationScore()) > 0 ? existing : replacement
                ))
                .values()
                .stream()
                .sorted((r1, r2) -> r2.getRecommendationScore().compareTo(r1.getRecommendationScore()))
                .limit(limit)
                .collect(Collectors.toList());
    }

    /**
     * 保存推荐结果
     */
    private void saveRecommendations(Long studentId, List<RecommendationResult> recommendations) {
        for (RecommendationResult result : recommendations) {
            PersonalizedRecommendation recommendation = new PersonalizedRecommendation();
            recommendation.setStudentId(studentId);
            recommendation.setRecommendationType(result.getRecommendationType());
            recommendation.setItemId(result.getItemId());
            recommendation.setItemName(result.getItemName());
            recommendation.setRecommendationScore(result.getRecommendationScore());
            recommendation.setReason(result.getReason());
            recommendation.setAlgorithmUsed(result.getAlgorithmUsed());
            recommendation.setRecommendationDate(LocalDateTime.now());
            recommendation.setStatus("ACTIVE");

            recommendationMapper.insert(recommendation);
        }
    }
}
