2025/06/19-20:11:02.282152 37ac RocksDB version: 7.7.3
2025/06/19-20:11:02.282254 37ac Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-20:11:02.282273 37ac Compile date 2022-10-24 17:17:55
2025/06/19-20:11:02.282296 37ac DB SUMMARY
2025/06/19-20:11:02.282308 37ac DB Session ID:  YAARR2AMVZBTGB1KBVTW
2025/06/19-20:11:02.283331 37ac CURRENT file:  CURRENT
2025/06/19-20:11:02.283362 37ac IDENTITY file:  IDENTITY
2025/06/19-20:11:02.283496 37ac MANIFEST file:  MANIFEST-000013 size: 340 Bytes
2025/06/19-20:11:02.283519 37ac SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log dir, Total Num: 2, files: 000010.sst 000011.sst 
2025/06/19-20:11:02.283536 37ac Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log: 000012.log size: 159 ; 
2025/06/19-20:11:02.283707 37ac                         Options.error_if_exists: 0
2025/06/19-20:11:02.283715 37ac                       Options.create_if_missing: 1
2025/06/19-20:11:02.283719 37ac                         Options.paranoid_checks: 1
2025/06/19-20:11:02.283723 37ac             Options.flush_verify_memtable_count: 1
2025/06/19-20:11:02.283726 37ac                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-20:11:02.283730 37ac        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-20:11:02.283738 37ac                                     Options.env: 0000018608B495C0
2025/06/19-20:11:02.283748 37ac                                      Options.fs: WinFS
2025/06/19-20:11:02.283751 37ac                                Options.info_log: 000001860AC0EC10
2025/06/19-20:11:02.283755 37ac                Options.max_file_opening_threads: 16
2025/06/19-20:11:02.283759 37ac                              Options.statistics: 0000018609F6FE70
2025/06/19-20:11:02.283763 37ac                               Options.use_fsync: 0
2025/06/19-20:11:02.283766 37ac                       Options.max_log_file_size: 0
2025/06/19-20:11:02.283770 37ac                  Options.max_manifest_file_size: 1073741824
2025/06/19-20:11:02.283774 37ac                   Options.log_file_time_to_roll: 0
2025/06/19-20:11:02.283778 37ac                       Options.keep_log_file_num: 100
2025/06/19-20:11:02.283781 37ac                    Options.recycle_log_file_num: 0
2025/06/19-20:11:02.283785 37ac                         Options.allow_fallocate: 1
2025/06/19-20:11:02.283788 37ac                        Options.allow_mmap_reads: 0
2025/06/19-20:11:02.283791 37ac                       Options.allow_mmap_writes: 0
2025/06/19-20:11:02.283795 37ac                        Options.use_direct_reads: 0
2025/06/19-20:11:02.283798 37ac                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-20:11:02.283802 37ac          Options.create_missing_column_families: 1
2025/06/19-20:11:02.283806 37ac                              Options.db_log_dir: 
2025/06/19-20:11:02.283809 37ac                                 Options.wal_dir: 
2025/06/19-20:11:02.283813 37ac                Options.table_cache_numshardbits: 6
2025/06/19-20:11:02.283818 37ac                         Options.WAL_ttl_seconds: 0
2025/06/19-20:11:02.283823 37ac                       Options.WAL_size_limit_MB: 0
2025/06/19-20:11:02.283827 37ac                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-20:11:02.283832 37ac             Options.manifest_preallocation_size: 4194304
2025/06/19-20:11:02.283836 37ac                     Options.is_fd_close_on_exec: 1
2025/06/19-20:11:02.283842 37ac                   Options.advise_random_on_open: 1
2025/06/19-20:11:02.283846 37ac                    Options.db_write_buffer_size: 0
2025/06/19-20:11:02.283849 37ac                    Options.write_buffer_manager: 0000018608B47220
2025/06/19-20:11:02.283853 37ac         Options.access_hint_on_compaction_start: 1
2025/06/19-20:11:02.283857 37ac           Options.random_access_max_buffer_size: 1048576
2025/06/19-20:11:02.283860 37ac                      Options.use_adaptive_mutex: 0
2025/06/19-20:11:02.283911 37ac                            Options.rate_limiter: 0000000000000000
2025/06/19-20:11:02.283924 37ac     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-20:11:02.283928 37ac                       Options.wal_recovery_mode: 2
2025/06/19-20:11:02.283932 37ac                  Options.enable_thread_tracking: 0
2025/06/19-20:11:02.283936 37ac                  Options.enable_pipelined_write: 0
2025/06/19-20:11:02.283939 37ac                  Options.unordered_write: 0
2025/06/19-20:11:02.283946 37ac         Options.allow_concurrent_memtable_write: 1
2025/06/19-20:11:02.283949 37ac      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-20:11:02.283953 37ac             Options.write_thread_max_yield_usec: 100
2025/06/19-20:11:02.283957 37ac            Options.write_thread_slow_yield_usec: 3
2025/06/19-20:11:02.283961 37ac                               Options.row_cache: None
2025/06/19-20:11:02.283967 37ac                              Options.wal_filter: None
2025/06/19-20:11:02.283971 37ac             Options.avoid_flush_during_recovery: 0
2025/06/19-20:11:02.283974 37ac             Options.allow_ingest_behind: 0
2025/06/19-20:11:02.283978 37ac             Options.two_write_queues: 0
2025/06/19-20:11:02.283982 37ac             Options.manual_wal_flush: 0
2025/06/19-20:11:02.283986 37ac             Options.wal_compression: 0
2025/06/19-20:11:02.283990 37ac             Options.atomic_flush: 0
2025/06/19-20:11:02.283993 37ac             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-20:11:02.283997 37ac                 Options.persist_stats_to_disk: 0
2025/06/19-20:11:02.284001 37ac                 Options.write_dbid_to_manifest: 0
2025/06/19-20:11:02.284004 37ac                 Options.log_readahead_size: 0
2025/06/19-20:11:02.284008 37ac                 Options.file_checksum_gen_factory: Unknown
2025/06/19-20:11:02.284012 37ac                 Options.best_efforts_recovery: 0
2025/06/19-20:11:02.284016 37ac                Options.max_bgerror_resume_count: 2147483647
2025/06/19-20:11:02.284020 37ac            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-20:11:02.284023 37ac             Options.allow_data_in_errors: 0
2025/06/19-20:11:02.284027 37ac             Options.db_host_id: __hostname__
2025/06/19-20:11:02.284031 37ac             Options.enforce_single_del_contracts: true
2025/06/19-20:11:02.284035 37ac             Options.max_background_jobs: 2
2025/06/19-20:11:02.284039 37ac             Options.max_background_compactions: 4
2025/06/19-20:11:02.284042 37ac             Options.max_subcompactions: 1
2025/06/19-20:11:02.284046 37ac             Options.avoid_flush_during_shutdown: 0
2025/06/19-20:11:02.284049 37ac           Options.writable_file_max_buffer_size: 1048576
2025/06/19-20:11:02.284053 37ac             Options.delayed_write_rate : 16777216
2025/06/19-20:11:02.284057 37ac             Options.max_total_wal_size: 1073741824
2025/06/19-20:11:02.284061 37ac             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-20:11:02.284065 37ac                   Options.stats_dump_period_sec: 600
2025/06/19-20:11:02.284068 37ac                 Options.stats_persist_period_sec: 600
2025/06/19-20:11:02.284072 37ac                 Options.stats_history_buffer_size: 1048576
2025/06/19-20:11:02.284076 37ac                          Options.max_open_files: -1
2025/06/19-20:11:02.284081 37ac                          Options.bytes_per_sync: 0
2025/06/19-20:11:02.284086 37ac                      Options.wal_bytes_per_sync: 0
2025/06/19-20:11:02.284091 37ac                   Options.strict_bytes_per_sync: 0
2025/06/19-20:11:02.284100 37ac       Options.compaction_readahead_size: 0
2025/06/19-20:11:02.284108 37ac                  Options.max_background_flushes: 1
2025/06/19-20:11:02.284114 37ac Compression algorithms supported:
2025/06/19-20:11:02.284136 37ac 	kZSTD supported: 1
2025/06/19-20:11:02.284146 37ac 	kSnappyCompression supported: 1
2025/06/19-20:11:02.284156 37ac 	kBZip2Compression supported: 0
2025/06/19-20:11:02.284165 37ac 	kZlibCompression supported: 1
2025/06/19-20:11:02.284207 37ac 	kLZ4Compression supported: 1
2025/06/19-20:11:02.284212 37ac 	kXpressCompression supported: 0
2025/06/19-20:11:02.284216 37ac 	kLZ4HCCompression supported: 1
2025/06/19-20:11:02.284220 37ac 	kZSTDNotFinalCompression supported: 1
2025/06/19-20:11:02.284232 37ac Fast CRC32 supported: Not supported on x86
2025/06/19-20:11:02.284236 37ac DMutex implementation: std::mutex
2025/06/19-20:11:02.286150 37ac [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000013
2025/06/19-20:11:02.286713 37ac [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-20:11:02.286740 37ac               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:11:02.286746 37ac           Options.merge_operator: StringAppendOperator
2025/06/19-20:11:02.286751 37ac        Options.compaction_filter: None
2025/06/19-20:11:02.286756 37ac        Options.compaction_filter_factory: None
2025/06/19-20:11:02.286760 37ac  Options.sst_partitioner_factory: None
2025/06/19-20:11:02.286764 37ac         Options.memtable_factory: SkipListFactory
2025/06/19-20:11:02.286768 37ac            Options.table_factory: BlockBasedTable
2025/06/19-20:11:02.286825 37ac            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000186071D0A70)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018609F6C0C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:11:02.286831 37ac        Options.write_buffer_size: 67108864
2025/06/19-20:11:02.286835 37ac  Options.max_write_buffer_number: 3
2025/06/19-20:11:02.286839 37ac          Options.compression: Snappy
2025/06/19-20:11:02.286844 37ac                  Options.bottommost_compression: Disabled
2025/06/19-20:11:02.286848 37ac       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:11:02.286852 37ac   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:11:02.286857 37ac             Options.num_levels: 7
2025/06/19-20:11:02.286861 37ac        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:11:02.286865 37ac     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:11:02.286868 37ac     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:11:02.286872 37ac            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:11:02.286877 37ac                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:11:02.286881 37ac               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:11:02.286885 37ac         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.286889 37ac         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.286893 37ac         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:11:02.286897 37ac                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:11:02.287003 37ac         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.287011 37ac         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.287016 37ac            Options.compression_opts.window_bits: -14
2025/06/19-20:11:02.287020 37ac                  Options.compression_opts.level: 32767
2025/06/19-20:11:02.287024 37ac               Options.compression_opts.strategy: 0
2025/06/19-20:11:02.287028 37ac         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.287032 37ac         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.287036 37ac         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.287040 37ac         Options.compression_opts.parallel_threads: 1
2025/06/19-20:11:02.287044 37ac                  Options.compression_opts.enabled: false
2025/06/19-20:11:02.287048 37ac         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.287052 37ac      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:11:02.287056 37ac          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:11:02.287071 37ac              Options.level0_stop_writes_trigger: 40
2025/06/19-20:11:02.287076 37ac                   Options.target_file_size_base: 67108864
2025/06/19-20:11:02.287106 37ac             Options.target_file_size_multiplier: 1
2025/06/19-20:11:02.287112 37ac                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:11:02.287116 37ac Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:11:02.287120 37ac          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:11:02.287141 37ac Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:11:02.287145 37ac Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:11:02.287149 37ac Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:11:02.287152 37ac Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:11:02.287156 37ac Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:11:02.287159 37ac Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:11:02.287163 37ac Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:11:02.287166 37ac       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:11:02.287170 37ac                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:11:02.287173 37ac                        Options.arena_block_size: 1048576
2025/06/19-20:11:02.287176 37ac   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:11:02.287180 37ac   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:11:02.287184 37ac                Options.disable_auto_compactions: 0
2025/06/19-20:11:02.287190 37ac                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:11:02.287195 37ac                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:11:02.287198 37ac Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:11:02.287202 37ac Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:11:02.287205 37ac Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:11:02.287209 37ac Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:11:02.287213 37ac Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:11:02.287218 37ac Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:11:02.287222 37ac Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:11:02.287225 37ac Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:11:02.287232 37ac                   Options.table_properties_collectors: 
2025/06/19-20:11:02.287236 37ac                   Options.inplace_update_support: 0
2025/06/19-20:11:02.287239 37ac                 Options.inplace_update_num_locks: 10000
2025/06/19-20:11:02.287243 37ac               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:11:02.287251 37ac               Options.memtable_whole_key_filtering: 0
2025/06/19-20:11:02.287256 37ac   Options.memtable_huge_page_size: 0
2025/06/19-20:11:02.287259 37ac                           Options.bloom_locality: 0
2025/06/19-20:11:02.287263 37ac                    Options.max_successive_merges: 0
2025/06/19-20:11:02.287267 37ac                Options.optimize_filters_for_hits: 0
2025/06/19-20:11:02.287270 37ac                Options.paranoid_file_checks: 0
2025/06/19-20:11:02.287273 37ac                Options.force_consistency_checks: 1
2025/06/19-20:11:02.287277 37ac                Options.report_bg_io_stats: 0
2025/06/19-20:11:02.287281 37ac                               Options.ttl: 2592000
2025/06/19-20:11:02.287284 37ac          Options.periodic_compaction_seconds: 0
2025/06/19-20:11:02.287288 37ac  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:11:02.287291 37ac                       Options.enable_blob_files: false
2025/06/19-20:11:02.287301 37ac                           Options.min_blob_size: 0
2025/06/19-20:11:02.287305 37ac                          Options.blob_file_size: 268435456
2025/06/19-20:11:02.287309 37ac                   Options.blob_compression_type: NoCompression
2025/06/19-20:11:02.287312 37ac          Options.enable_blob_garbage_collection: false
2025/06/19-20:11:02.287316 37ac      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:11:02.287320 37ac Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:11:02.287324 37ac          Options.blob_compaction_readahead_size: 0
2025/06/19-20:11:02.287327 37ac                Options.blob_file_starting_level: 0
2025/06/19-20:11:02.287331 37ac Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:11:02.289504 37ac [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-20:11:02.289526 37ac               Options.comparator: leveldb.BytewiseComparator
2025/06/19-20:11:02.289531 37ac           Options.merge_operator: StringAppendOperator
2025/06/19-20:11:02.289534 37ac        Options.compaction_filter: None
2025/06/19-20:11:02.289538 37ac        Options.compaction_filter_factory: None
2025/06/19-20:11:02.289542 37ac  Options.sst_partitioner_factory: None
2025/06/19-20:11:02.289545 37ac         Options.memtable_factory: SkipListFactory
2025/06/19-20:11:02.289549 37ac            Options.table_factory: BlockBasedTable
2025/06/19-20:11:02.289582 37ac            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000186071D0A70)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 0000018609F6C0C0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-20:11:02.289586 37ac        Options.write_buffer_size: 67108864
2025/06/19-20:11:02.289590 37ac  Options.max_write_buffer_number: 3
2025/06/19-20:11:02.289594 37ac          Options.compression: Snappy
2025/06/19-20:11:02.289598 37ac                  Options.bottommost_compression: Disabled
2025/06/19-20:11:02.289602 37ac       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-20:11:02.289610 37ac   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-20:11:02.289615 37ac             Options.num_levels: 7
2025/06/19-20:11:02.289618 37ac        Options.min_write_buffer_number_to_merge: 1
2025/06/19-20:11:02.289622 37ac     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-20:11:02.289626 37ac     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-20:11:02.289630 37ac            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-20:11:02.289634 37ac                  Options.bottommost_compression_opts.level: 32767
2025/06/19-20:11:02.289646 37ac               Options.bottommost_compression_opts.strategy: 0
2025/06/19-20:11:02.289650 37ac         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.289654 37ac         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.289658 37ac         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-20:11:02.289662 37ac                  Options.bottommost_compression_opts.enabled: false
2025/06/19-20:11:02.289665 37ac         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.289669 37ac         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.289673 37ac            Options.compression_opts.window_bits: -14
2025/06/19-20:11:02.289676 37ac                  Options.compression_opts.level: 32767
2025/06/19-20:11:02.289680 37ac               Options.compression_opts.strategy: 0
2025/06/19-20:11:02.289684 37ac         Options.compression_opts.max_dict_bytes: 0
2025/06/19-20:11:02.289687 37ac         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-20:11:02.289691 37ac         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-20:11:02.289695 37ac         Options.compression_opts.parallel_threads: 1
2025/06/19-20:11:02.289698 37ac                  Options.compression_opts.enabled: false
2025/06/19-20:11:02.289702 37ac         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-20:11:02.289706 37ac      Options.level0_file_num_compaction_trigger: 10
2025/06/19-20:11:02.289709 37ac          Options.level0_slowdown_writes_trigger: 20
2025/06/19-20:11:02.289713 37ac              Options.level0_stop_writes_trigger: 40
2025/06/19-20:11:02.289716 37ac                   Options.target_file_size_base: 67108864
2025/06/19-20:11:02.289720 37ac             Options.target_file_size_multiplier: 1
2025/06/19-20:11:02.289724 37ac                Options.max_bytes_for_level_base: 536870912
2025/06/19-20:11:02.289727 37ac Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-20:11:02.289731 37ac          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-20:11:02.289735 37ac Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-20:11:02.289739 37ac Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-20:11:02.289743 37ac Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-20:11:02.289747 37ac Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-20:11:02.289750 37ac Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-20:11:02.289754 37ac Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-20:11:02.289758 37ac Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-20:11:02.289761 37ac       Options.max_sequential_skip_in_iterations: 8
2025/06/19-20:11:02.289765 37ac                    Options.max_compaction_bytes: 1677721600
2025/06/19-20:11:02.289768 37ac                        Options.arena_block_size: 1048576
2025/06/19-20:11:02.289772 37ac   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-20:11:02.289776 37ac   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-20:11:02.289780 37ac                Options.disable_auto_compactions: 0
2025/06/19-20:11:02.289784 37ac                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-20:11:02.289788 37ac                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-20:11:02.289793 37ac Options.compaction_options_universal.size_ratio: 1
2025/06/19-20:11:02.289798 37ac Options.compaction_options_universal.min_merge_width: 2
2025/06/19-20:11:02.289801 37ac Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-20:11:02.289805 37ac Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-20:11:02.289815 37ac Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-20:11:02.289820 37ac Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-20:11:02.289823 37ac Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-20:11:02.289827 37ac Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-20:11:02.289834 37ac                   Options.table_properties_collectors: 
2025/06/19-20:11:02.289838 37ac                   Options.inplace_update_support: 0
2025/06/19-20:11:02.289842 37ac                 Options.inplace_update_num_locks: 10000
2025/06/19-20:11:02.289846 37ac               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-20:11:02.289850 37ac               Options.memtable_whole_key_filtering: 0
2025/06/19-20:11:02.289853 37ac   Options.memtable_huge_page_size: 0
2025/06/19-20:11:02.289857 37ac                           Options.bloom_locality: 0
2025/06/19-20:11:02.289861 37ac                    Options.max_successive_merges: 0
2025/06/19-20:11:02.289864 37ac                Options.optimize_filters_for_hits: 0
2025/06/19-20:11:02.289868 37ac                Options.paranoid_file_checks: 0
2025/06/19-20:11:02.289872 37ac                Options.force_consistency_checks: 1
2025/06/19-20:11:02.289875 37ac                Options.report_bg_io_stats: 0
2025/06/19-20:11:02.289878 37ac                               Options.ttl: 2592000
2025/06/19-20:11:02.289882 37ac          Options.periodic_compaction_seconds: 0
2025/06/19-20:11:02.289886 37ac  Options.preclude_last_level_data_seconds: 0
2025/06/19-20:11:02.289894 37ac                       Options.enable_blob_files: false
2025/06/19-20:11:02.289897 37ac                           Options.min_blob_size: 0
2025/06/19-20:11:02.289901 37ac                          Options.blob_file_size: 268435456
2025/06/19-20:11:02.289905 37ac                   Options.blob_compression_type: NoCompression
2025/06/19-20:11:02.289909 37ac          Options.enable_blob_garbage_collection: false
2025/06/19-20:11:02.289912 37ac      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-20:11:02.289916 37ac Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-20:11:02.289920 37ac          Options.blob_compaction_readahead_size: 0
2025/06/19-20:11:02.289924 37ac                Options.blob_file_starting_level: 0
2025/06/19-20:11:02.289928 37ac Options.experimental_mempurge_threshold: 0.000000
2025/06/19-20:11:02.294936 37ac [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/MANIFEST-000013 succeeded,manifest_file_number is 13, next_file_number is 15, last_sequence is 2, log_number is 5,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 5
2025/06/19-20:11:02.294955 37ac [db\version_set.cc:5588] Column family [default] (ID 0), log number is 5
2025/06/19-20:11:02.294958 37ac [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 5
2025/06/19-20:11:02.295375 37ac [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9d-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-20:11:02.296862 37ac EVENT_LOG_v1 {"time_micros": 1750335062296841, "job": 1, "event": "recovery_started", "wal_files": [12]}
2025/06/19-20:11:02.296880 37ac [db\db_impl\db_impl_open.cc:1029] Recovering log #12 mode 2
2025/06/19-20:11:02.299343 37ac EVENT_LOG_v1 {"time_micros": 1750335062299290, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 16, "file_size": 1117, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 4, "largest_seqno": 4, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335062, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "YAARR2AMVZBTGB1KBVTW", "orig_file_number": 16, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:11:02.303934 37ac EVENT_LOG_v1 {"time_micros": 1750335062303899, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 17, "file_size": 1181, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 3, "largest_seqno": 5, "table_properties": {"data_size": 103, "index_size": 65, "index_partitions": 1, "top_level_index_size": 30, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 42, "raw_average_key_size": 21, "raw_value_size": 42, "raw_average_value_size": 21, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750335062, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9d-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "YAARR2AMVZBTGB1KBVTW", "orig_file_number": 17, "seqno_to_time_mapping": "N/A"}}
2025/06/19-20:11:02.307419 37ac EVENT_LOG_v1 {"time_micros": 1750335062307408, "job": 1, "event": "recovery_finished"}
2025/06/19-20:11:02.308444 37ac [db\version_set.cc:5051] Creating manifest 19
2025/06/19-20:11:02.317365 37ac [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_persistent_service_v2\log/000012.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-20:11:02.317414 37ac [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 0000018608AD03A0
2025/06/19-20:11:02.318237 37ac DB pointer 000001860DFE0040
2025/06/19-20:11:02.318804 5be8 [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-20:11:02.318819 5be8 [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.18 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    2.18 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018609F6C0C0#21528 capacity: 512.00 MB usage: 0.79 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(4,0.37 KB,7.0408e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    2.25 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    2.25 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.6      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.04 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.04 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0000018609F6C0C0#21528 capacity: 512.00 MB usage: 0.79 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): IndexBlock(4,0.37 KB,7.0408e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 4 Average: 49.7500  StdDev: 31.64
Min: 10  Median: 51.0000  Max: 96
Percentiles: P50: 51.00 P75: 76.00 P99: 96.00 P99.9: 96.00 P99.99: 96.00
------------------------------------------------------
(       6,      10 ]        1  25.000%  25.000% #####
(      34,      51 ]        1  25.000%  50.000% #####
(      51,      76 ]        1  25.000%  75.000% #####
(      76,     110 ]        1  25.000% 100.000% #####


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 4 Average: 27.5000  StdDev: 16.83
Min: 8  Median: 15.0000  Max: 50
Percentiles: P50: 15.00 P75: 42.50 P99: 50.00 P99.9: 50.00 P99.99: 50.00
------------------------------------------------------
(       6,      10 ]        1  25.000%  25.000% #####
(      10,      15 ]        1  25.000%  50.000% #####
(      34,      51 ]        2  50.000% 100.000% ##########

2025/06/19-20:11:02.319591 5be8 [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 4
rocksdb.block.cache.hit COUNT : 0
rocksdb.block.cache.add COUNT : 4
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 4
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 4
rocksdb.block.cache.index.bytes.insert COUNT : 378
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 0
rocksdb.block.cache.data.hit COUNT : 0
rocksdb.block.cache.data.add COUNT : 0
rocksdb.block.cache.data.bytes.insert COUNT : 0
rocksdb.block.cache.bytes.read COUNT : 0
rocksdb.block.cache.bytes.write COUNT : 378
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 0
rocksdb.l0.hit COUNT : 0
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 0
rocksdb.number.keys.read COUNT : 0
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 0
rocksdb.bytes.read COUNT : 0
rocksdb.number.db.seek COUNT : 0
rocksdb.number.db.next COUNT : 0
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 0
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 0
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 4
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 0
rocksdb.wal.bytes COUNT : 0
rocksdb.write.self COUNT : 0
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 0
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2298
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 0
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 0
rocksdb.num.iterator.deleted COUNT : 0
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 9076
rocksdb.non.last.level.read.count COUNT : 8
rocksdb.block.checksum.compute.count COUNT : 12
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 580.000000 P95 : 687.000000 P99 : 687.000000 P100 : 687.000000 COUNT : 2 SUM : 1262
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.manifest.file.sync.micros P50 : 397.000000 P95 : 397.000000 P99 : 397.000000 P100 : 397.000000 COUNT : 1 SUM : 397
rocksdb.table.open.io.micros P50 : 170.000000 P95 : 739.000000 P99 : 739.000000 P100 : 739.000000 COUNT : 4 SUM : 1190
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 1.333333 P95 : 5.600000 P99 : 5.920000 P100 : 6.000000 COUNT : 8 SUM : 20
rocksdb.write.raw.block.micros P50 : 0.833333 P95 : 18.500000 P99 : 19.000000 P100 : 19.000000 COUNT : 10 SUM : 30
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 39.666667 P95 : 96.000000 P99 : 96.000000 P100 : 96.000000 COUNT : 8 SUM : 309
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.write P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1043.000000 P95 : 1043.000000 P99 : 1043.000000 P100 : 1043.000000 COUNT : 4 SUM : 4127
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-20:11:03.318668 5be8 [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-20:11:03.318692 5be8 [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-20:11:03.318695 5be8 [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
