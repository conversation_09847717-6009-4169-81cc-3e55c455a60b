package edu.tsinghua.studentmis.grade.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import edu.tsinghua.studentmis.grade.entity.GradeRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 成绩记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface GradeRecordMapper extends BaseMapper<GradeRecord> {

    /**
     * 查询学生成绩
     */
    @Select("SELECT * FROM grade_record WHERE student_id = #{studentId} AND schedule_id = #{scheduleId}")
    List<GradeRecord> selectStudentGrades(@Param("studentId") Long studentId,
                                         @Param("scheduleId") Long scheduleId);
}
