package edu.tsinghua.studentmis.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import edu.tsinghua.studentmis.analytics.entity.LearningBehaviorAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习行为分析Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Mapper
public interface LearningBehaviorAnalysisMapper extends BaseMapper<LearningBehaviorAnalysis> {

    /**
     * 根据学生ID查询学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE student_id = #{studentId} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByStudentId(@Param("studentId") Long studentId);

    /**
     * 根据学生ID和学期ID查询学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE student_id = #{studentId} AND semester_id = #{semesterId} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByStudentIdAndSemesterId(@Param("studentId") Long studentId, 
                                                                  @Param("semesterId") Long semesterId);

    /**
     * 根据分析类型查询学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE analysis_type = #{analysisType} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByAnalysisType(@Param("analysisType") String analysisType);

    /**
     * 查询指定时间范围内的学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE start_time >= #{startTime} AND end_time <= #{endTime} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 根据学生ID和分析类型查询最新的分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE student_id = #{studentId} AND analysis_type = #{analysisType} AND deleted = 0 ORDER BY created_at DESC LIMIT 1")
    LearningBehaviorAnalysis selectLatestByStudentIdAndType(@Param("studentId") Long studentId, 
                                                           @Param("analysisType") String analysisType);

    /**
     * 根据分析状态查询学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE analysis_status = #{status} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByAnalysisStatus(@Param("status") String status);

    /**
     * 查询指定学期的所有学习行为分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE semester_id = #{semesterId} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectBySemesterId(@Param("semesterId") Long semesterId);

    /**
     * 统计学生的分析记录数量
     */
    @Select("SELECT COUNT(*) FROM learning_behavior_analysis WHERE student_id = #{studentId} AND deleted = 0")
    Integer countByStudentId(@Param("studentId") Long studentId);

    /**
     * 查询学生在指定时间范围内的分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE student_id = #{studentId} AND created_at >= #{startTime} AND created_at <= #{endTime} AND deleted = 0 ORDER BY created_at DESC")
    List<LearningBehaviorAnalysis> selectByStudentIdAndCreateTime(@Param("studentId") Long studentId,
                                                                 @Param("startTime") LocalDateTime startTime,
                                                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询高置信度的分析记录
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE confidence >= #{minConfidence} AND deleted = 0 ORDER BY confidence DESC, created_at DESC")
    List<LearningBehaviorAnalysis> selectByMinConfidence(@Param("minConfidence") Double minConfidence);

    // Service期望的数据查询方法
    /**
     * 获取登录行为数据
     */
    @Select("SELECT * FROM student_login_log WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY login_time DESC")
    List<Map<String, Object>> getLoginBehaviorData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取学习时长数据
     */
    @Select("SELECT * FROM study_time_log WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY study_date DESC")
    List<Map<String, Object>> getStudyTimeData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取作业数据
     */
    @Select("SELECT * FROM assignment_submission WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY submission_date DESC")
    List<Map<String, Object>> getAssignmentData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取出勤数据
     */
    @Select("SELECT * FROM attendance_record WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY attendance_date DESC")
    List<Map<String, Object>> getAttendanceData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取论坛参与数据
     */
    @Select("SELECT * FROM forum_participation WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY participation_date DESC")
    List<Map<String, Object>> getForumParticipationData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取资源访问数据
     */
    @Select("SELECT * FROM resource_access_log WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY access_time DESC")
    List<Map<String, Object>> getResourceAccessData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取测验数据
     */
    @Select("SELECT * FROM quiz_attempt WHERE student_id = #{studentId} AND semester_id = #{semesterId} ORDER BY attempt_time DESC")
    List<Map<String, Object>> getQuizData(@Param("studentId") Long studentId, @Param("semesterId") Long semesterId);

    /**
     * 获取最新的行为分析结果
     */
    @Select("SELECT * FROM learning_behavior_analysis WHERE student_id = #{studentId} AND deleted = 0 ORDER BY created_at DESC LIMIT 1")
    LearningBehaviorAnalysis getLatestByStudentId(@Param("studentId") Long studentId);
}
