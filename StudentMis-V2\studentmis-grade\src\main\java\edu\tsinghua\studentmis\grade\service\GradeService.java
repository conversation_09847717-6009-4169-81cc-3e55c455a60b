package edu.tsinghua.studentmis.grade.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import edu.tsinghua.studentmis.common.exception.BusinessException;
import edu.tsinghua.studentmis.common.result.ResultCode;
import edu.tsinghua.studentmis.common.util.SecurityUtils;
import edu.tsinghua.studentmis.grade.dto.*;
import edu.tsinghua.studentmis.grade.entity.GradeRecord;
import edu.tsinghua.studentmis.grade.mapper.GradeRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 成绩服务实现类
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GradeService extends ServiceImpl<GradeRecordMapper, GradeRecord> {

    private final GradeRecordMapper gradeRecordMapper;

    /**
     * 分页查询成绩记录
     */
    public IPage<GradeRecordVO> pageGradeRecords(GradeQueryRequest request) {
        Page<GradeRecord> page = new Page<>(request.getCurrent(), request.getSize());
        
        LambdaQueryWrapper<GradeRecord> wrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        wrapper.eq(request.getStudentId() != null, GradeRecord::getStudentId, request.getStudentId())
               .eq(request.getScheduleId() != null, GradeRecord::getScheduleId, request.getScheduleId())
               .eq(request.getGradeType() != null, GradeRecord::getGradeType, request.getGradeType())
               .eq(request.getAuditStatus() != null, GradeRecord::getAuditStatus, request.getAuditStatus())
               .eq(request.getIsPass() != null, GradeRecord::getIsPass, request.getIsPass())
               .ge(request.getTotalScoreMin() != null, GradeRecord::getTotalScore, request.getTotalScoreMin())
               .le(request.getTotalScoreMax() != null, GradeRecord::getTotalScore, request.getTotalScoreMax())
               .ge(request.getExamDateStart() != null, GradeRecord::getExamDate, request.getExamDateStart())
               .le(request.getExamDateEnd() != null, GradeRecord::getExamDate, request.getExamDateEnd())
               .orderByDesc(GradeRecord::getCreatedAt);
        
        IPage<GradeRecord> gradeRecordPage = gradeRecordMapper.selectPage(page, wrapper);
        
        // 转换为VO
        return gradeRecordPage.convert(this::convertToVO);
    }

    /**
     * 根据ID获取成绩详细信息
     */
    public GradeRecordVO getGradeRecordById(Long id) {
        GradeRecord gradeRecord = gradeRecordMapper.selectById(id);
        if (gradeRecord == null) {
            throw new BusinessException(ResultCode.GRADE_NOT_FOUND);
        }
        
        return convertToVO(gradeRecord);
    }

    /**
     * 录入成绩
     */
    @Transactional
    public GradeRecordVO inputGrade(GradeInputRequest request) {
        // 检查成绩是否已存在
        if (existsGradeRecord(request.getStudentId(), request.getScheduleId(),
                GradeRecord.GradeType.fromString(request.getGradeType()))) {
            throw new BusinessException(ResultCode.GRADE_ALREADY_EXISTS);
        }
        
        GradeRecord gradeRecord = new GradeRecord();
        BeanUtil.copyProperties(request, gradeRecord);
        
        // 计算总成绩和绩点
        gradeRecord.calculateTotalScore();
        gradeRecord.calculateGradePoint();
        
        // 设置录入信息
        gradeRecord.setInputBy(SecurityUtils.getCurrentUserId());
        gradeRecord.setInputTime(LocalDateTime.now());
        gradeRecord.setAuditStatus(GradeRecord.AuditStatus.PENDING);
        
        // 保存成绩记录
        gradeRecordMapper.insert(gradeRecord);
        
        log.info("录入成绩成功: studentId={}, scheduleId={}, totalScore={}", 
                gradeRecord.getStudentId(), gradeRecord.getScheduleId(), gradeRecord.getTotalScore());
        
        return convertToVO(gradeRecord);
    }

    /**
     * 更新成绩
     */
    @Transactional
    public GradeRecordVO updateGrade(Long id, GradeUpdateRequest request) {
        GradeRecord existingRecord = gradeRecordMapper.selectById(id);
        if (existingRecord == null) {
            throw new BusinessException(ResultCode.GRADE_NOT_FOUND);
        }
        
        // 检查成绩是否已审核通过
        if (existingRecord.isAuditApproved()) {
            throw new BusinessException(ResultCode.GRADE_LOCKED, "成绩已审核通过，无法修改");
        }
        
        // 更新成绩信息
        BeanUtil.copyProperties(request, existingRecord, "id", "createdAt", "createdBy");
        
        // 重新计算总成绩和绩点
        existingRecord.calculateTotalScore();
        existingRecord.calculateGradePoint();
        
        // 重置审核状态
        existingRecord.setAuditStatus(GradeRecord.AuditStatus.PENDING);
        existingRecord.setAuditBy(null);
        existingRecord.setAuditTime(null);
        existingRecord.setAuditComment(null);
        
        gradeRecordMapper.updateById(existingRecord);
        
        log.info("更新成绩成功: id={}, totalScore={}", id, existingRecord.getTotalScore());
        
        return convertToVO(existingRecord);
    }

    /**
     * 审核成绩
     */
    @Transactional
    public void auditGrade(Long id, GradeAuditRequest request) {
        GradeRecord gradeRecord = gradeRecordMapper.selectById(id);
        if (gradeRecord == null) {
            throw new BusinessException(ResultCode.GRADE_NOT_FOUND);
        }
        
        // 检查审核状态
        if (gradeRecord.isAuditApproved()) {
            throw new BusinessException(ResultCode.GRADE_AUDIT_FAILED, "成绩已审核通过");
        }
        
        // 更新审核信息
        gradeRecord.setAuditStatus(GradeRecord.AuditStatus.fromString(request.getAuditStatus()));
        gradeRecord.setAuditBy(SecurityUtils.getCurrentUserId());
        gradeRecord.setAuditTime(LocalDateTime.now());
        gradeRecord.setAuditComment(request.getAuditComment());
        
        gradeRecordMapper.updateById(gradeRecord);
        
        log.info("审核成绩成功: id={}, auditStatus={}", id, request.getAuditStatus());
    }

    /**
     * 删除成绩记录
     */
    @Transactional
    public void deleteGradeRecord(Long id) {
        GradeRecord gradeRecord = gradeRecordMapper.selectById(id);
        if (gradeRecord == null) {
            throw new BusinessException(ResultCode.GRADE_NOT_FOUND);
        }
        
        // 检查成绩是否已审核通过
        if (gradeRecord.isAuditApproved()) {
            throw new BusinessException(ResultCode.GRADE_LOCKED, "成绩已审核通过，无法删除");
        }
        
        // 逻辑删除
        gradeRecordMapper.deleteById(id);
        
        log.info("删除成绩记录成功: id={}", id);
    }

    /**
     * 批量录入成绩
     */
    @Transactional
    public List<GradeRecordVO> batchInputGrades(List<GradeInputRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "批量录入数据不能为空");
        }
        
        return requests.stream()
                .map(this::inputGrade)
                .collect(Collectors.toList());
    }

    /**
     * 获取学生成绩统计
     */
    public StudentGradeStatisticsVO getStudentGradeStatistics(Long studentId, Long semesterId) {
        List<GradeRecord> gradeRecords = gradeRecordMapper.selectStudentGrades(studentId, semesterId);
        
        if (gradeRecords.isEmpty()) {
            return StudentGradeStatisticsVO.builder()
                    .studentId(studentId)
                    .semesterId(semesterId)
                    .totalCourses(0)
                    .passedCourses(0)
                    .failedCourses(0)
                    .totalCredits(BigDecimal.ZERO)
                    .earnedCredits(BigDecimal.ZERO)
                    .gpa(BigDecimal.ZERO)
                    .weightedScore(BigDecimal.ZERO)
                    .build();
        }
        
        // 计算统计数据
        int totalCourses = gradeRecords.size();
        int passedCourses = (int) gradeRecords.stream().filter(GradeRecord::isPassed).count();
        int failedCourses = totalCourses - passedCourses;
        
        BigDecimal totalCredits = gradeRecords.stream()
                .map(record -> new BigDecimal("3.0")) // 假设每门课3学分
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal earnedCredits = gradeRecords.stream()
                .filter(GradeRecord::isPassed)
                .map(record -> new BigDecimal("3.0"))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 计算GPA
        BigDecimal totalGradePoints = gradeRecords.stream()
                .map(record -> record.getGradePoint().multiply(new BigDecimal("3.0")))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal gpa = totalCredits.compareTo(BigDecimal.ZERO) > 0 ? 
                totalGradePoints.divide(totalCredits, 3, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        
        // 计算加权平均分
        BigDecimal totalWeightedScore = gradeRecords.stream()
                .map(record -> record.getTotalScore().multiply(new BigDecimal("3.0")))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal weightedScore = totalCredits.compareTo(BigDecimal.ZERO) > 0 ? 
                totalWeightedScore.divide(totalCredits, 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
        
        return StudentGradeStatisticsVO.builder()
                .studentId(studentId)
                .semesterId(semesterId)
                .totalCourses(totalCourses)
                .passedCourses(passedCourses)
                .failedCourses(failedCourses)
                .totalCredits(totalCredits)
                .earnedCredits(earnedCredits)
                .gpa(gpa)
                .weightedScore(weightedScore)
                .build();
    }

    /**
     * 检查成绩记录是否存在
     */
    private boolean existsGradeRecord(Long studentId, Long scheduleId, GradeRecord.GradeType gradeType) {
        LambdaQueryWrapper<GradeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GradeRecord::getStudentId, studentId)
               .eq(GradeRecord::getScheduleId, scheduleId)
               .eq(GradeRecord::getGradeType, gradeType);
        return gradeRecordMapper.selectCount(wrapper) > 0;
    }

    /**
     * 转换为VO对象
     */
    private GradeRecordVO convertToVO(GradeRecord gradeRecord) {
        GradeRecordVO vo = new GradeRecordVO();
        BeanUtil.copyProperties(gradeRecord, vo);
        return vo;
    }
}
