server:
  port: 8080

spring:
  application:
    name: studentmis-gateway
  profiles:
    active: dev
  main:
    web-application-type: reactive
  config:
    import: "optional:nacos:studentmis-gateway.yml"
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: studentmis-v2
        group: DEFAULT_GROUP
      config:
        server-addr: localhost:8848
        namespace: studentmis-v2
        group: DEFAULT_GROUP
        file-extension: yml
        shared-configs:
          - data-id: common-config.yml
            group: DEFAULT_GROUP
            refresh: true
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 认证服务路由
        - id: studentmis-auth
          uri: lb://studentmis-auth
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2
        
        # 学生服务路由
        - id: studentmis-student
          uri: lb://studentmis-student
          predicates:
            - Path=/api/student/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
        
        # 课程服务路由
        - id: studentmis-course
          uri: lb://studentmis-course
          predicates:
            - Path=/api/course/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
        
        # 成绩服务路由
        - id: studentmis-grade
          uri: lb://studentmis-grade
          predicates:
            - Path=/api/grade/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
        
        # 数据分析服务路由
        - id: studentmis-analytics
          uri: lb://studentmis-analytics
          predicates:
            - Path=/api/analytics/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
        
        # 通知服务路由
        - id: studentmis-notification
          uri: lb://studentmis-notification
          predicates:
            - Path=/api/notification/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
      
      # 全局过滤器配置
      default-filters:
        - name: Retry
          args:
            retries: 3
            methods: GET,POST,PUT,DELETE
        - name: RequestRateLimiter
          args:
            redis-rate-limiter.replenishRate: 100
            redis-rate-limiter.burstCapacity: 200
            redis-rate-limiter.requestedTokens: 1
        - name: CircuitBreaker
          args:
            name: default
            fallbackUri: forward:/fallback

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 日志配置
logging:
  level:
    edu.tsinghua.studentmis: DEBUG
    org.springframework.cloud.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/gateway.log

# JWT配置
jwt:
  secret: studentmis-v2-secret-key-for-jwt-token-generation
  expiration: 86400
  header: Authorization
  prefix: "Bearer "

# 网关配置
gateway:
  # 跨域配置
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
    allow-credentials: true
  
  # 限流配置
  rate-limit:
    enabled: true
    default-rate: 100
    default-capacity: 200
  
  # 熔断配置
  circuit-breaker:
    enabled: true
    failure-rate-threshold: 50
    wait-duration-in-open-state: 30s
    sliding-window-size: 10
