package edu.tsinghua.studentmis.grade.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import edu.tsinghua.studentmis.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 成绩记录实体
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("grade_record")
@Schema(description = "成绩记录")
public class GradeRecord extends BaseEntity {

    @Schema(description = "学生ID")
    @NotNull(message = "学生ID不能为空")
    @TableField("student_id")
    private Long studentId;

    @Schema(description = "课程安排ID")
    @NotNull(message = "课程安排ID不能为空")
    @TableField("schedule_id")
    private Long scheduleId;

    @Schema(description = "成绩类型")
    @NotNull(message = "成绩类型不能为空")
    @TableField("grade_type")
    private GradeType gradeType;

    @Schema(description = "平时成绩")
    @DecimalMin(value = "0.00", message = "平时成绩不能小于0")
    @DecimalMax(value = "100.00", message = "平时成绩不能大于100")
    @Digits(integer = 3, fraction = 2, message = "平时成绩格式不正确")
    @TableField("usual_score")
    private BigDecimal usualScore;

    @Schema(description = "期中成绩")
    @DecimalMin(value = "0.00", message = "期中成绩不能小于0")
    @DecimalMax(value = "100.00", message = "期中成绩不能大于100")
    @Digits(integer = 3, fraction = 2, message = "期中成绩格式不正确")
    @TableField("midterm_score")
    private BigDecimal midtermScore;

    @Schema(description = "期末成绩")
    @DecimalMin(value = "0.00", message = "期末成绩不能小于0")
    @DecimalMax(value = "100.00", message = "期末成绩不能大于100")
    @Digits(integer = 3, fraction = 2, message = "期末成绩格式不正确")
    @TableField("final_score")
    private BigDecimal finalScore;

    @Schema(description = "总成绩")
    @DecimalMin(value = "0.00", message = "总成绩不能小于0")
    @DecimalMax(value = "100.00", message = "总成绩不能大于100")
    @Digits(integer = 3, fraction = 2, message = "总成绩格式不正确")
    @TableField("total_score")
    private BigDecimal totalScore;

    @Schema(description = "绩点")
    @DecimalMin(value = "0.00", message = "绩点不能小于0")
    @DecimalMax(value = "4.00", message = "绩点不能大于4.0")
    @Digits(integer = 1, fraction = 2, message = "绩点格式不正确")
    @TableField("grade_point")
    private BigDecimal gradePoint;

    @Schema(description = "等级成绩")
    @Size(max = 2, message = "等级成绩长度不能超过2个字符")
    @TableField("letter_grade")
    private String letterGrade;

    @Schema(description = "是否通过")
    @TableField("is_pass")
    private Boolean isPass;

    @Schema(description = "班级排名")
    @Min(value = 1, message = "班级排名必须大于0")
    @TableField("rank_in_class")
    private Integer rankInClass;

    @Schema(description = "百分位数")
    @DecimalMin(value = "0.00", message = "百分位数不能小于0")
    @DecimalMax(value = "100.00", message = "百分位数不能大于100")
    @Digits(integer = 3, fraction = 2, message = "百分位数格式不正确")
    @TableField("percentile")
    private BigDecimal percentile;

    @Schema(description = "考试日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField("exam_date")
    private LocalDate examDate;

    @Schema(description = "录入人ID")
    @NotNull(message = "录入人ID不能为空")
    @TableField("input_by")
    private Long inputBy;

    @Schema(description = "录入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("input_time")
    private LocalDateTime inputTime;

    @Schema(description = "审核人ID")
    @TableField("audit_by")
    private Long auditBy;

    @Schema(description = "审核状态")
    @TableField("audit_status")
    private AuditStatus auditStatus;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("audit_time")
    private LocalDateTime auditTime;

    @Schema(description = "审核意见")
    @Size(max = 255, message = "审核意见长度不能超过255个字符")
    @TableField("audit_comment")
    private String auditComment;

    @Schema(description = "备注")
    @TableField("remarks")
    private String remarks;

    /**
     * 成绩类型枚举
     */
    public enum GradeType {
        REGULAR("正常考试"),
        MAKEUP("补考"),
        RETAKE("重修");

        private final String description;

        GradeType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 从字符串转换为枚举
         */
        public static GradeType fromString(String value) {
            if (value == null || value.trim().isEmpty()) {
                return REGULAR; // 默认为正常考试
            }

            String upperValue = value.toUpperCase().trim();
            try {
                return GradeType.valueOf(upperValue);
            } catch (IllegalArgumentException e) {
                // 尝试通过描述匹配
                for (GradeType type : GradeType.values()) {
                    if (type.description.equals(value.trim())) {
                        return type;
                    }
                }
                throw new IllegalArgumentException("无效的成绩类型: " + value);
            }
        }
    }

    /**
     * 审核状态枚举
     */
    public enum AuditStatus {
        PENDING("待审核"),
        APPROVED("已通过"),
        REJECTED("已拒绝");

        private final String description;

        AuditStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 从字符串转换为枚举
         */
        public static AuditStatus fromString(String value) {
            if (value == null || value.trim().isEmpty()) {
                return PENDING; // 默认为待审核
            }

            String upperValue = value.toUpperCase().trim();
            try {
                return AuditStatus.valueOf(upperValue);
            } catch (IllegalArgumentException e) {
                // 尝试通过描述匹配
                for (AuditStatus status : AuditStatus.values()) {
                    if (status.description.equals(value.trim())) {
                        return status;
                    }
                }
                throw new IllegalArgumentException("无效的审核状态: " + value);
            }
        }
    }

    /**
     * 计算总成绩
     * 平时成绩30% + 期中成绩30% + 期末成绩40%
     */
    public void calculateTotalScore() {
        if (usualScore != null && midtermScore != null && finalScore != null) {
            BigDecimal total = usualScore.multiply(new BigDecimal("0.3"))
                    .add(midtermScore.multiply(new BigDecimal("0.3")))
                    .add(finalScore.multiply(new BigDecimal("0.4")));
            this.totalScore = total.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算绩点
     */
    public void calculateGradePoint() {
        if (totalScore != null) {
            if (totalScore.compareTo(new BigDecimal("90")) >= 0) {
                this.gradePoint = new BigDecimal("4.0");
                this.letterGrade = "A";
            } else if (totalScore.compareTo(new BigDecimal("85")) >= 0) {
                this.gradePoint = new BigDecimal("3.7");
                this.letterGrade = "A-";
            } else if (totalScore.compareTo(new BigDecimal("82")) >= 0) {
                this.gradePoint = new BigDecimal("3.3");
                this.letterGrade = "B+";
            } else if (totalScore.compareTo(new BigDecimal("78")) >= 0) {
                this.gradePoint = new BigDecimal("3.0");
                this.letterGrade = "B";
            } else if (totalScore.compareTo(new BigDecimal("75")) >= 0) {
                this.gradePoint = new BigDecimal("2.7");
                this.letterGrade = "B-";
            } else if (totalScore.compareTo(new BigDecimal("72")) >= 0) {
                this.gradePoint = new BigDecimal("2.3");
                this.letterGrade = "C+";
            } else if (totalScore.compareTo(new BigDecimal("68")) >= 0) {
                this.gradePoint = new BigDecimal("2.0");
                this.letterGrade = "C";
            } else if (totalScore.compareTo(new BigDecimal("64")) >= 0) {
                this.gradePoint = new BigDecimal("1.7");
                this.letterGrade = "C-";
            } else if (totalScore.compareTo(new BigDecimal("60")) >= 0) {
                this.gradePoint = new BigDecimal("1.0");
                this.letterGrade = "D";
            } else {
                this.gradePoint = new BigDecimal("0.0");
                this.letterGrade = "F";
            }
            
            this.isPass = totalScore.compareTo(new BigDecimal("60")) >= 0;
        }
    }

    /**
     * 检查成绩是否通过
     */
    public boolean isPassed() {
        return Boolean.TRUE.equals(this.isPass);
    }

    /**
     * 检查成绩是否已审核通过
     */
    public boolean isAuditApproved() {
        return AuditStatus.APPROVED.equals(this.auditStatus);
    }
}
