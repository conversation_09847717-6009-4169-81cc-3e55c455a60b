2025/06/19-19:27:56.160327 4be8 RocksDB version: 7.7.3
2025/06/19-19:27:56.160413 4be8 Git sha eb9a80fe1f18017b4d7f4084e8f2554f12234822
2025/06/19-19:27:56.160431 4be8 Compile date 2022-10-24 17:17:55
2025/06/19-19:27:56.160441 4be8 DB SUMMARY
2025/06/19-19:27:56.160452 4be8 DB Session ID:  DEIGZIB5FYX57QXAYUQD
2025/06/19-19:27:56.161182 4be8 CURRENT file:  CURRENT
2025/06/19-19:27:56.161199 4be8 IDENTITY file:  IDENTITY
2025/06/19-19:27:56.161321 4be8 MANIFEST file:  MANIFEST-000005 size: 119 Bytes
2025/06/19-19:27:56.161336 4be8 SST files in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log dir, Total Num: 0, files: 
2025/06/19-19:27:56.161348 4be8 Write Ahead Log file in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log: 000004.log size: 110 ; 
2025/06/19-19:27:56.161551 4be8                         Options.error_if_exists: 0
2025/06/19-19:27:56.161564 4be8                       Options.create_if_missing: 1
2025/06/19-19:27:56.161569 4be8                         Options.paranoid_checks: 1
2025/06/19-19:27:56.161573 4be8             Options.flush_verify_memtable_count: 1
2025/06/19-19:27:56.161576 4be8                               Options.track_and_verify_wals_in_manifest: 0
2025/06/19-19:27:56.161580 4be8        Options.verify_sst_unique_id_in_manifest: 1
2025/06/19-19:27:56.161584 4be8                                     Options.env: 00000282D34347A0
2025/06/19-19:27:56.161588 4be8                                      Options.fs: WinFS
2025/06/19-19:27:56.161592 4be8                                Options.info_log: 00000282D0128040
2025/06/19-19:27:56.161596 4be8                Options.max_file_opening_threads: 16
2025/06/19-19:27:56.161600 4be8                              Options.statistics: 00000282FE5C6F30
2025/06/19-19:27:56.161604 4be8                               Options.use_fsync: 0
2025/06/19-19:27:56.161608 4be8                       Options.max_log_file_size: 0
2025/06/19-19:27:56.161615 4be8                  Options.max_manifest_file_size: 1073741824
2025/06/19-19:27:56.161619 4be8                   Options.log_file_time_to_roll: 0
2025/06/19-19:27:56.161623 4be8                       Options.keep_log_file_num: 100
2025/06/19-19:27:56.161626 4be8                    Options.recycle_log_file_num: 0
2025/06/19-19:27:56.161630 4be8                         Options.allow_fallocate: 1
2025/06/19-19:27:56.161634 4be8                        Options.allow_mmap_reads: 0
2025/06/19-19:27:56.161637 4be8                       Options.allow_mmap_writes: 0
2025/06/19-19:27:56.161641 4be8                        Options.use_direct_reads: 0
2025/06/19-19:27:56.161645 4be8                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/19-19:27:56.161648 4be8          Options.create_missing_column_families: 1
2025/06/19-19:27:56.161652 4be8                              Options.db_log_dir: 
2025/06/19-19:27:56.161656 4be8                                 Options.wal_dir: 
2025/06/19-19:27:56.161659 4be8                Options.table_cache_numshardbits: 6
2025/06/19-19:27:56.161662 4be8                         Options.WAL_ttl_seconds: 0
2025/06/19-19:27:56.161666 4be8                       Options.WAL_size_limit_MB: 0
2025/06/19-19:27:56.161669 4be8                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/19-19:27:56.161673 4be8             Options.manifest_preallocation_size: 4194304
2025/06/19-19:27:56.161676 4be8                     Options.is_fd_close_on_exec: 1
2025/06/19-19:27:56.161679 4be8                   Options.advise_random_on_open: 1
2025/06/19-19:27:56.161683 4be8                    Options.db_write_buffer_size: 0
2025/06/19-19:27:56.161686 4be8                    Options.write_buffer_manager: 00000282D3435E20
2025/06/19-19:27:56.161689 4be8         Options.access_hint_on_compaction_start: 1
2025/06/19-19:27:56.161693 4be8           Options.random_access_max_buffer_size: 1048576
2025/06/19-19:27:56.161696 4be8                      Options.use_adaptive_mutex: 0
2025/06/19-19:27:56.161743 4be8                            Options.rate_limiter: 0000000000000000
2025/06/19-19:27:56.161749 4be8     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/19-19:27:56.161753 4be8                       Options.wal_recovery_mode: 2
2025/06/19-19:27:56.161756 4be8                  Options.enable_thread_tracking: 0
2025/06/19-19:27:56.161759 4be8                  Options.enable_pipelined_write: 0
2025/06/19-19:27:56.161762 4be8                  Options.unordered_write: 0
2025/06/19-19:27:56.161765 4be8         Options.allow_concurrent_memtable_write: 1
2025/06/19-19:27:56.161769 4be8      Options.enable_write_thread_adaptive_yield: 1
2025/06/19-19:27:56.161773 4be8             Options.write_thread_max_yield_usec: 100
2025/06/19-19:27:56.161777 4be8            Options.write_thread_slow_yield_usec: 3
2025/06/19-19:27:56.161780 4be8                               Options.row_cache: None
2025/06/19-19:27:56.161784 4be8                              Options.wal_filter: None
2025/06/19-19:27:56.161787 4be8             Options.avoid_flush_during_recovery: 0
2025/06/19-19:27:56.161790 4be8             Options.allow_ingest_behind: 0
2025/06/19-19:27:56.161794 4be8             Options.two_write_queues: 0
2025/06/19-19:27:56.161796 4be8             Options.manual_wal_flush: 0
2025/06/19-19:27:56.161799 4be8             Options.wal_compression: 0
2025/06/19-19:27:56.161802 4be8             Options.atomic_flush: 0
2025/06/19-19:27:56.161806 4be8             Options.avoid_unnecessary_blocking_io: 0
2025/06/19-19:27:56.161809 4be8                 Options.persist_stats_to_disk: 0
2025/06/19-19:27:56.161812 4be8                 Options.write_dbid_to_manifest: 0
2025/06/19-19:27:56.161816 4be8                 Options.log_readahead_size: 0
2025/06/19-19:27:56.161819 4be8                 Options.file_checksum_gen_factory: Unknown
2025/06/19-19:27:56.161823 4be8                 Options.best_efforts_recovery: 0
2025/06/19-19:27:56.161826 4be8                Options.max_bgerror_resume_count: 2147483647
2025/06/19-19:27:56.161829 4be8            Options.bgerror_resume_retry_interval: 1000000
2025/06/19-19:27:56.161833 4be8             Options.allow_data_in_errors: 0
2025/06/19-19:27:56.161836 4be8             Options.db_host_id: __hostname__
2025/06/19-19:27:56.161839 4be8             Options.enforce_single_del_contracts: true
2025/06/19-19:27:56.161842 4be8             Options.max_background_jobs: 2
2025/06/19-19:27:56.161846 4be8             Options.max_background_compactions: 4
2025/06/19-19:27:56.161849 4be8             Options.max_subcompactions: 1
2025/06/19-19:27:56.161858 4be8             Options.avoid_flush_during_shutdown: 0
2025/06/19-19:27:56.161879 4be8           Options.writable_file_max_buffer_size: 1048576
2025/06/19-19:27:56.161887 4be8             Options.delayed_write_rate : 16777216
2025/06/19-19:27:56.161890 4be8             Options.max_total_wal_size: 1073741824
2025/06/19-19:27:56.161893 4be8             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/19-19:27:56.161895 4be8                   Options.stats_dump_period_sec: 600
2025/06/19-19:27:56.161897 4be8                 Options.stats_persist_period_sec: 600
2025/06/19-19:27:56.161900 4be8                 Options.stats_history_buffer_size: 1048576
2025/06/19-19:27:56.161902 4be8                          Options.max_open_files: -1
2025/06/19-19:27:56.161905 4be8                          Options.bytes_per_sync: 0
2025/06/19-19:27:56.161907 4be8                      Options.wal_bytes_per_sync: 0
2025/06/19-19:27:56.161909 4be8                   Options.strict_bytes_per_sync: 0
2025/06/19-19:27:56.161912 4be8       Options.compaction_readahead_size: 0
2025/06/19-19:27:56.161914 4be8                  Options.max_background_flushes: 1
2025/06/19-19:27:56.161916 4be8 Compression algorithms supported:
2025/06/19-19:27:56.161920 4be8 	kZSTD supported: 1
2025/06/19-19:27:56.161922 4be8 	kSnappyCompression supported: 1
2025/06/19-19:27:56.161925 4be8 	kBZip2Compression supported: 0
2025/06/19-19:27:56.161927 4be8 	kZlibCompression supported: 1
2025/06/19-19:27:56.161973 4be8 	kLZ4Compression supported: 1
2025/06/19-19:27:56.161977 4be8 	kXpressCompression supported: 0
2025/06/19-19:27:56.161980 4be8 	kLZ4HCCompression supported: 1
2025/06/19-19:27:56.161982 4be8 	kZSTDNotFinalCompression supported: 1
2025/06/19-19:27:56.161986 4be8 Fast CRC32 supported: Not supported on x86
2025/06/19-19:27:56.161988 4be8 DMutex implementation: std::mutex
2025/06/19-19:27:56.163026 4be8 [db\version_set.cc:5531] Recovering from manifest file: D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000005
2025/06/19-19:27:56.163197 4be8 [db\column_family.cc:633] --------------- Options for column family [default]:
2025/06/19-19:27:56.163204 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.163207 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.163209 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.163211 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.163214 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.163216 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.163219 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.163244 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282D2C051A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D4010AE0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.163249 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.163251 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.163254 4be8          Options.compression: Snappy
2025/06/19-19:27:56.163256 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.163259 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.163261 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.163263 4be8             Options.num_levels: 7
2025/06/19-19:27:56.163266 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.163268 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.163270 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.163272 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.163275 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.163277 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.163279 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.163282 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.163284 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.163287 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.163289 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.163323 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.163327 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.163329 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.163331 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.163334 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.163336 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.163338 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.163341 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.163343 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.163345 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.163348 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.163350 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.163352 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.163355 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.163357 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.163359 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.163362 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.163364 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.163367 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.163370 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.163372 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.163374 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.163376 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.163379 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.163381 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.163383 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.163386 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.163388 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.163390 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.163393 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.163395 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.163398 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.163401 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.163404 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.163406 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.163408 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.163411 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.163413 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.163416 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.163419 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.163421 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.163425 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.163427 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.163429 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.163432 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.163436 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.163439 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.163441 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.163444 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.163446 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.163449 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.163451 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.163454 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.163456 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.163458 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.163461 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.163463 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.163466 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.163469 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.163471 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.163474 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.163476 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.163481 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.163483 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.163486 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.163488 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.164991 4be8 [db\column_family.cc:633] --------------- Options for column family [Configuration]:
2025/06/19-19:27:56.165003 4be8               Options.comparator: leveldb.BytewiseComparator
2025/06/19-19:27:56.165006 4be8           Options.merge_operator: StringAppendOperator
2025/06/19-19:27:56.165008 4be8        Options.compaction_filter: None
2025/06/19-19:27:56.165010 4be8        Options.compaction_filter_factory: None
2025/06/19-19:27:56.165012 4be8  Options.sst_partitioner_factory: None
2025/06/19-19:27:56.165015 4be8         Options.memtable_factory: SkipListFactory
2025/06/19-19:27:56.165018 4be8            Options.table_factory: BlockBasedTable
2025/06/19-19:27:56.165051 4be8            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (00000282D2C051A0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 1
  pin_top_level_index_and_filter: 1
  index_type: 2
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 1
  no_block_cache: 0
  block_cache: 00000282D4010AE0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 8
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  block_cache_compressed: 0000000000000000
  persistent_cache: 0000000000000000
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 8192
  partition_filters: 1
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/19-19:27:56.165058 4be8        Options.write_buffer_size: 67108864
2025/06/19-19:27:56.165060 4be8  Options.max_write_buffer_number: 3
2025/06/19-19:27:56.165063 4be8          Options.compression: Snappy
2025/06/19-19:27:56.165065 4be8                  Options.bottommost_compression: Disabled
2025/06/19-19:27:56.165068 4be8       Options.prefix_extractor: rocksdb.FixedPrefix
2025/06/19-19:27:56.165076 4be8   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/19-19:27:56.165079 4be8             Options.num_levels: 7
2025/06/19-19:27:56.165081 4be8        Options.min_write_buffer_number_to_merge: 1
2025/06/19-19:27:56.165083 4be8     Options.max_write_buffer_number_to_maintain: 0
2025/06/19-19:27:56.165085 4be8     Options.max_write_buffer_size_to_maintain: 0
2025/06/19-19:27:56.165088 4be8            Options.bottommost_compression_opts.window_bits: -14
2025/06/19-19:27:56.165090 4be8                  Options.bottommost_compression_opts.level: 32767
2025/06/19-19:27:56.165093 4be8               Options.bottommost_compression_opts.strategy: 0
2025/06/19-19:27:56.165095 4be8         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.165097 4be8         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.165100 4be8         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/19-19:27:56.165102 4be8                  Options.bottommost_compression_opts.enabled: false
2025/06/19-19:27:56.165104 4be8         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.165107 4be8         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.165109 4be8            Options.compression_opts.window_bits: -14
2025/06/19-19:27:56.165112 4be8                  Options.compression_opts.level: 32767
2025/06/19-19:27:56.165114 4be8               Options.compression_opts.strategy: 0
2025/06/19-19:27:56.165116 4be8         Options.compression_opts.max_dict_bytes: 0
2025/06/19-19:27:56.165119 4be8         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/19-19:27:56.165121 4be8         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/19-19:27:56.165123 4be8         Options.compression_opts.parallel_threads: 1
2025/06/19-19:27:56.165126 4be8                  Options.compression_opts.enabled: false
2025/06/19-19:27:56.165128 4be8         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/19-19:27:56.165130 4be8      Options.level0_file_num_compaction_trigger: 10
2025/06/19-19:27:56.165133 4be8          Options.level0_slowdown_writes_trigger: 20
2025/06/19-19:27:56.165135 4be8              Options.level0_stop_writes_trigger: 40
2025/06/19-19:27:56.165137 4be8                   Options.target_file_size_base: 67108864
2025/06/19-19:27:56.165140 4be8             Options.target_file_size_multiplier: 1
2025/06/19-19:27:56.165142 4be8                Options.max_bytes_for_level_base: 536870912
2025/06/19-19:27:56.165144 4be8 Options.level_compaction_dynamic_level_bytes: 0
2025/06/19-19:27:56.165147 4be8          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/19-19:27:56.165150 4be8 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/19-19:27:56.165152 4be8 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/19-19:27:56.165154 4be8 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/19-19:27:56.165157 4be8 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/19-19:27:56.165159 4be8 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/19-19:27:56.165161 4be8 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/19-19:27:56.165164 4be8 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/19-19:27:56.165166 4be8       Options.max_sequential_skip_in_iterations: 8
2025/06/19-19:27:56.165168 4be8                    Options.max_compaction_bytes: 1677721600
2025/06/19-19:27:56.165171 4be8                        Options.arena_block_size: 1048576
2025/06/19-19:27:56.165173 4be8   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/19-19:27:56.165176 4be8   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/19-19:27:56.165178 4be8                Options.disable_auto_compactions: 0
2025/06/19-19:27:56.165181 4be8                        Options.compaction_style: kCompactionStyleLevel
2025/06/19-19:27:56.165184 4be8                          Options.compaction_pri: kMinOverlappingRatio
2025/06/19-19:27:56.165186 4be8 Options.compaction_options_universal.size_ratio: 1
2025/06/19-19:27:56.165189 4be8 Options.compaction_options_universal.min_merge_width: 2
2025/06/19-19:27:56.165192 4be8 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/19-19:27:56.165195 4be8 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/19-19:27:56.165197 4be8 Options.compaction_options_universal.compression_size_percent: -1
2025/06/19-19:27:56.165200 4be8 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/19-19:27:56.165203 4be8 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/19-19:27:56.165205 4be8 Options.compaction_options_fifo.allow_compaction: 0
2025/06/19-19:27:56.165210 4be8                   Options.table_properties_collectors: 
2025/06/19-19:27:56.165212 4be8                   Options.inplace_update_support: 0
2025/06/19-19:27:56.165215 4be8                 Options.inplace_update_num_locks: 10000
2025/06/19-19:27:56.165217 4be8               Options.memtable_prefix_bloom_size_ratio: 0.125000
2025/06/19-19:27:56.165220 4be8               Options.memtable_whole_key_filtering: 0
2025/06/19-19:27:56.165222 4be8   Options.memtable_huge_page_size: 0
2025/06/19-19:27:56.165225 4be8                           Options.bloom_locality: 0
2025/06/19-19:27:56.165227 4be8                    Options.max_successive_merges: 0
2025/06/19-19:27:56.165229 4be8                Options.optimize_filters_for_hits: 0
2025/06/19-19:27:56.165232 4be8                Options.paranoid_file_checks: 0
2025/06/19-19:27:56.165234 4be8                Options.force_consistency_checks: 1
2025/06/19-19:27:56.165237 4be8                Options.report_bg_io_stats: 0
2025/06/19-19:27:56.165239 4be8                               Options.ttl: 2592000
2025/06/19-19:27:56.165241 4be8          Options.periodic_compaction_seconds: 0
2025/06/19-19:27:56.165244 4be8  Options.preclude_last_level_data_seconds: 0
2025/06/19-19:27:56.165246 4be8                       Options.enable_blob_files: false
2025/06/19-19:27:56.165248 4be8                           Options.min_blob_size: 0
2025/06/19-19:27:56.165251 4be8                          Options.blob_file_size: 268435456
2025/06/19-19:27:56.165253 4be8                   Options.blob_compression_type: NoCompression
2025/06/19-19:27:56.165256 4be8          Options.enable_blob_garbage_collection: false
2025/06/19-19:27:56.165258 4be8      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/19-19:27:56.165261 4be8 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/19-19:27:56.165263 4be8          Options.blob_compaction_readahead_size: 0
2025/06/19-19:27:56.165266 4be8                Options.blob_file_starting_level: 0
2025/06/19-19:27:56.165268 4be8 Options.experimental_mempurge_threshold: 0.000000
2025/06/19-19:27:56.168604 4be8 [db\version_set.cc:5579] Recovered from manifest file:D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/MANIFEST-000005 succeeded,manifest_file_number is 5, next_file_number is 7, last_sequence is 0, log_number is 4,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/06/19-19:27:56.168619 4be8 [db\version_set.cc:5588] Column family [default] (ID 0), log number is 0
2025/06/19-19:27:56.168622 4be8 [db\version_set.cc:5588] Column family [Configuration] (ID 1), log number is 4
2025/06/19-19:27:56.169004 4be8 [db\db_impl\db_impl_open.cc:529] DB ID: 33632c9e-4d00-11f0-b610-c88a9a6cfecf
2025/06/19-19:27:56.170006 4be8 EVENT_LOG_v1 {"time_micros": 1750332476169998, "job": 1, "event": "recovery_started", "wal_files": [4]}
2025/06/19-19:27:56.170013 4be8 [db\db_impl\db_impl_open.cc:1029] Recovering log #4 mode 2
2025/06/19-19:27:56.171670 4be8 EVENT_LOG_v1 {"time_micros": 1750332476171635, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 10, "file_size": 1117, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 1, "largest_seqno": 1, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQD", "orig_file_number": 10, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.175290 4be8 EVENT_LOG_v1 {"time_micros": 1750332476175254, "cf_name": "Configuration", "job": 1, "event": "table_file_creation", "file_number": 11, "file_size": 1123, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 2, "largest_seqno": 2, "table_properties": {"data_size": 66, "index_size": 45, "index_partitions": 1, "top_level_index_size": 20, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 34, "raw_average_value_size": 34, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "Configuration", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "merge_operator": "StringAppendOperator", "prefix_extractor_name": "rocksdb.FixedPrefix.8", "property_collectors": "[]", "compression": "Snappy", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1750332476, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "33632c9e-4d00-11f0-b610-c88a9a6cfecf", "db_session_id": "DEIGZIB5FYX57QXAYUQD", "orig_file_number": 11, "seqno_to_time_mapping": "N/A"}}
2025/06/19-19:27:56.178166 4be8 EVENT_LOG_v1 {"time_micros": 1750332476178159, "job": 1, "event": "recovery_finished"}
2025/06/19-19:27:56.178633 4be8 [db\version_set.cc:5051] Creating manifest 13
2025/06/19-19:27:56.185667 4be8 [file\delete_scheduler.cc:77] Deleted file D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\data\protocol\raft\naming_instance_metadata\log/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/19-19:27:56.185698 4be8 [db\db_impl\db_impl_open.cc:1985] SstFileManager instance 00000282D1B3D070
2025/06/19-19:27:56.186514 4be8 DB pointer 00000282D1CC9080
2025/06/19-19:27:59.191395 3edc [db\db_impl\db_impl.cc:1101] ------- DUMPING STATS -------
2025/06/19-19:27:59.191427 3edc [db\db_impl\db_impl.cc:1102] 
** DB Stats **
Uptime(secs): 3.0 total, 3.0 interval
Cumulative writes: 2 writes, 3 keys, 2 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 2 writes, 2 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 2 writes, 3 keys, 2 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 2 writes, 2 syncs, 1.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.09 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.09 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D4010AE0#9160 capacity: 512.00 MB usage: 0.86 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000148 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.26 KB,4.95464e-05%) IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** Compaction Stats [Configuration] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.10 KB   0.1      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.10 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [Configuration] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 3.0 total, 3.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@00000282D4010AE0#9160 capacity: 512.00 MB usage: 0.86 KB table_size: 4096 occupancy: 5 collections: 1 last_copies: 1 last_secs: 0.000148 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,0.26 KB,4.95464e-05%) IndexBlock(2,0.18 KB,3.42727e-05%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
** Level 0 read latency histogram (micros):
Count: 3 Average: 11.0000  StdDev: 3.74
Min: 6  Median: 11.2500  Max: 15
Percentiles: P50: 11.25 P75: 13.12 P99: 14.92 P99.9: 14.99 P99.99: 15.00
------------------------------------------------------
(       4,       6 ]        1  33.333%  33.333% #######
(      10,      15 ]        2  66.667% 100.000% #############


** File Read Latency Histogram By Level [Configuration] **
** Level 0 read latency histogram (micros):
Count: 3 Average: 12.0000  StdDev: 2.94
Min: 8  Median: 11.2500  Max: 15
Percentiles: P50: 11.25 P75: 13.12 P99: 14.92 P99.9: 14.99 P99.99: 15.00
------------------------------------------------------
(       6,      10 ]        1  33.333%  33.333% #######
(      10,      15 ]        2  66.667% 100.000% #############

2025/06/19-19:27:59.192074 3edc [db\db_impl\db_impl.cc:789] STATISTICS:
 rocksdb.block.cache.miss COUNT : 4
rocksdb.block.cache.hit COUNT : 4
rocksdb.block.cache.add COUNT : 4
rocksdb.block.cache.add.failures COUNT : 0
rocksdb.block.cache.index.miss COUNT : 2
rocksdb.block.cache.index.hit COUNT : 0
rocksdb.block.cache.index.add COUNT : 2
rocksdb.block.cache.index.bytes.insert COUNT : 184
rocksdb.block.cache.index.bytes.evict COUNT : 0
rocksdb.block.cache.filter.miss COUNT : 0
rocksdb.block.cache.filter.hit COUNT : 0
rocksdb.block.cache.filter.add COUNT : 0
rocksdb.block.cache.filter.bytes.insert COUNT : 0
rocksdb.block.cache.filter.bytes.evict COUNT : 0
rocksdb.block.cache.data.miss COUNT : 2
rocksdb.block.cache.data.hit COUNT : 4
rocksdb.block.cache.data.add COUNT : 2
rocksdb.block.cache.data.bytes.insert COUNT : 266
rocksdb.block.cache.bytes.read COUNT : 532
rocksdb.block.cache.bytes.write COUNT : 450
rocksdb.bloom.filter.useful COUNT : 0
rocksdb.bloom.filter.full.positive COUNT : 0
rocksdb.bloom.filter.full.true.positive COUNT : 0
rocksdb.bloom.filter.micros COUNT : 0
rocksdb.persistent.cache.hit COUNT : 0
rocksdb.persistent.cache.miss COUNT : 0
rocksdb.sim.block.cache.hit COUNT : 0
rocksdb.sim.block.cache.miss COUNT : 0
rocksdb.memtable.hit COUNT : 0
rocksdb.memtable.miss COUNT : 3
rocksdb.l0.hit COUNT : 3
rocksdb.l1.hit COUNT : 0
rocksdb.l2andup.hit COUNT : 0
rocksdb.compaction.key.drop.new COUNT : 0
rocksdb.compaction.key.drop.obsolete COUNT : 0
rocksdb.compaction.key.drop.range_del COUNT : 0
rocksdb.compaction.key.drop.user COUNT : 0
rocksdb.compaction.range_del.drop.obsolete COUNT : 0
rocksdb.compaction.optimized.del.drop.obsolete COUNT : 0
rocksdb.compaction.cancelled COUNT : 0
rocksdb.number.keys.written COUNT : 3
rocksdb.number.keys.read COUNT : 3
rocksdb.number.keys.updated COUNT : 0
rocksdb.bytes.written COUNT : 145
rocksdb.bytes.read COUNT : 102
rocksdb.number.db.seek COUNT : 3
rocksdb.number.db.next COUNT : 1
rocksdb.number.db.prev COUNT : 0
rocksdb.number.db.seek.found COUNT : 3
rocksdb.number.db.next.found COUNT : 0
rocksdb.number.db.prev.found COUNT : 0
rocksdb.db.iter.bytes.read COUNT : 126
rocksdb.no.file.closes COUNT : 0
rocksdb.no.file.opens COUNT : 2
rocksdb.no.file.errors COUNT : 0
rocksdb.l0.slowdown.micros COUNT : 0
rocksdb.memtable.compaction.micros COUNT : 0
rocksdb.l0.num.files.stall.micros COUNT : 0
rocksdb.stall.micros COUNT : 0
rocksdb.db.mutex.wait.micros COUNT : 0
rocksdb.rate.limit.delay.millis COUNT : 0
rocksdb.num.iterators COUNT : 0
rocksdb.number.multiget.get COUNT : 0
rocksdb.number.multiget.keys.read COUNT : 0
rocksdb.number.multiget.bytes.read COUNT : 0
rocksdb.number.deletes.filtered COUNT : 0
rocksdb.number.merge.failures COUNT : 0
rocksdb.bloom.filter.prefix.checked COUNT : 0
rocksdb.bloom.filter.prefix.useful COUNT : 0
rocksdb.number.reseeks.iteration COUNT : 0
rocksdb.getupdatessince.calls COUNT : 0
rocksdb.block.cachecompressed.miss COUNT : 0
rocksdb.block.cachecompressed.hit COUNT : 0
rocksdb.block.cachecompressed.add COUNT : 0
rocksdb.block.cachecompressed.add.failures COUNT : 0
rocksdb.wal.synced COUNT : 2
rocksdb.wal.bytes COUNT : 145
rocksdb.write.self COUNT : 2
rocksdb.write.other COUNT : 0
rocksdb.write.timeout COUNT : 0
rocksdb.write.wal COUNT : 2
rocksdb.compact.read.bytes COUNT : 0
rocksdb.compact.write.bytes COUNT : 2240
rocksdb.flush.write.bytes COUNT : 0
rocksdb.compact.read.marked.bytes COUNT : 0
rocksdb.compact.read.periodic.bytes COUNT : 0
rocksdb.compact.read.ttl.bytes COUNT : 0
rocksdb.compact.write.marked.bytes COUNT : 0
rocksdb.compact.write.periodic.bytes COUNT : 0
rocksdb.compact.write.ttl.bytes COUNT : 0
rocksdb.number.direct.load.table.properties COUNT : 0
rocksdb.number.superversion_acquires COUNT : 3
rocksdb.number.superversion_releases COUNT : 0
rocksdb.number.superversion_cleanups COUNT : 0
rocksdb.number.block.compressed COUNT : 0
rocksdb.number.block.decompressed COUNT : 0
rocksdb.number.block.not_compressed COUNT : 6
rocksdb.merge.operation.time.nanos COUNT : 0
rocksdb.filter.operation.time.nanos COUNT : 0
rocksdb.row.cache.hit COUNT : 0
rocksdb.row.cache.miss COUNT : 0
rocksdb.read.amp.estimate.useful.bytes COUNT : 0
rocksdb.read.amp.total.read.bytes COUNT : 0
rocksdb.number.rate_limiter.drains COUNT : 0
rocksdb.number.iter.skip COUNT : 0
rocksdb.blobdb.num.put COUNT : 0
rocksdb.blobdb.num.write COUNT : 0
rocksdb.blobdb.num.get COUNT : 0
rocksdb.blobdb.num.multiget COUNT : 0
rocksdb.blobdb.num.seek COUNT : 0
rocksdb.blobdb.num.next COUNT : 0
rocksdb.blobdb.num.prev COUNT : 0
rocksdb.blobdb.num.keys.written COUNT : 0
rocksdb.blobdb.num.keys.read COUNT : 0
rocksdb.blobdb.bytes.written COUNT : 0
rocksdb.blobdb.bytes.read COUNT : 0
rocksdb.blobdb.write.inlined COUNT : 0
rocksdb.blobdb.write.inlined.ttl COUNT : 0
rocksdb.blobdb.write.blob COUNT : 0
rocksdb.blobdb.write.blob.ttl COUNT : 0
rocksdb.blobdb.blob.file.bytes.written COUNT : 0
rocksdb.blobdb.blob.file.bytes.read COUNT : 0
rocksdb.blobdb.blob.file.synced COUNT : 0
rocksdb.blobdb.blob.index.expired.count COUNT : 0
rocksdb.blobdb.blob.index.expired.size COUNT : 0
rocksdb.blobdb.blob.index.evicted.count COUNT : 0
rocksdb.blobdb.blob.index.evicted.size COUNT : 0
rocksdb.blobdb.gc.num.files COUNT : 0
rocksdb.blobdb.gc.num.new.files COUNT : 0
rocksdb.blobdb.gc.failures COUNT : 0
rocksdb.blobdb.gc.num.keys.overwritten COUNT : 0
rocksdb.blobdb.gc.num.keys.expired COUNT : 0
rocksdb.blobdb.gc.num.keys.relocated COUNT : 0
rocksdb.blobdb.gc.bytes.overwritten COUNT : 0
rocksdb.blobdb.gc.bytes.expired COUNT : 0
rocksdb.blobdb.gc.bytes.relocated COUNT : 0
rocksdb.blobdb.fifo.num.files.evicted COUNT : 0
rocksdb.blobdb.fifo.num.keys.evicted COUNT : 0
rocksdb.blobdb.fifo.bytes.evicted COUNT : 0
rocksdb.txn.overhead.mutex.prepare COUNT : 0
rocksdb.txn.overhead.mutex.old.commit.map COUNT : 0
rocksdb.txn.overhead.duplicate.key COUNT : 0
rocksdb.txn.overhead.mutex.snapshot COUNT : 0
rocksdb.txn.get.tryagain COUNT : 0
rocksdb.number.multiget.keys.found COUNT : 0
rocksdb.num.iterator.created COUNT : 3
rocksdb.num.iterator.deleted COUNT : 3
rocksdb.block.cache.compression.dict.miss COUNT : 0
rocksdb.block.cache.compression.dict.hit COUNT : 0
rocksdb.block.cache.compression.dict.add COUNT : 0
rocksdb.block.cache.compression.dict.bytes.insert COUNT : 0
rocksdb.block.cache.compression.dict.bytes.evict COUNT : 0
rocksdb.block.cache.add.redundant COUNT : 0
rocksdb.block.cache.index.add.redundant COUNT : 0
rocksdb.block.cache.filter.add.redundant COUNT : 0
rocksdb.block.cache.data.add.redundant COUNT : 0
rocksdb.block.cache.compression.dict.add.redundant COUNT : 0
rocksdb.files.marked.trash COUNT : 0
rocksdb.files.deleted.immediately COUNT : 0
rocksdb.error.handler.bg.errro.count COUNT : 0
rocksdb.error.handler.bg.io.errro.count COUNT : 0
rocksdb.error.handler.bg.retryable.io.errro.count COUNT : 0
rocksdb.error.handler.autoresume.count COUNT : 0
rocksdb.error.handler.autoresume.retry.total.count COUNT : 0
rocksdb.error.handler.autoresume.success.count COUNT : 0
rocksdb.memtable.payload.bytes.at.flush COUNT : 0
rocksdb.memtable.garbage.bytes.at.flush COUNT : 0
rocksdb.secondary.cache.hits COUNT : 0
rocksdb.verify_checksum.read.bytes COUNT : 0
rocksdb.backup.read.bytes COUNT : 0
rocksdb.backup.write.bytes COUNT : 0
rocksdb.remote.compact.read.bytes COUNT : 0
rocksdb.remote.compact.write.bytes COUNT : 0
rocksdb.hot.file.read.bytes COUNT : 0
rocksdb.warm.file.read.bytes COUNT : 0
rocksdb.cold.file.read.bytes COUNT : 0
rocksdb.hot.file.read.count COUNT : 0
rocksdb.warm.file.read.count COUNT : 0
rocksdb.cold.file.read.count COUNT : 0
rocksdb.last.level.read.bytes COUNT : 0
rocksdb.last.level.read.count COUNT : 0
rocksdb.non.last.level.read.bytes COUNT : 4612
rocksdb.non.last.level.read.count COUNT : 6
rocksdb.block.checksum.compute.count COUNT : 8
rocksdb.multiget.coroutine.count COUNT : 0
rocksdb.blobdb.cache.miss COUNT : 0
rocksdb.blobdb.cache.hit COUNT : 0
rocksdb.blobdb.cache.add COUNT : 0
rocksdb.blobdb.cache.add.failures COUNT : 0
rocksdb.blobdb.cache.bytes.read COUNT : 0
rocksdb.blobdb.cache.bytes.write COUNT : 0
rocksdb.db.get.micros P50 : 25.000000 P95 : 27.000000 P99 : 27.000000 P100 : 27.000000 COUNT : 3 SUM : 62
rocksdb.db.write.micros P50 : 580.000000 P95 : 654.000000 P99 : 654.000000 P100 : 654.000000 COUNT : 2 SUM : 1092
rocksdb.compaction.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compaction.times.cpu_micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.subcompaction.setup.times.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.table.sync.micros P50 : 474.000000 P95 : 474.000000 P99 : 474.000000 P100 : 474.000000 COUNT : 2 SUM : 920
rocksdb.compaction.outfile.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.wal.file.sync.micros P50 : 480.000000 P95 : 490.000000 P99 : 490.000000 P100 : 490.000000 COUNT : 2 SUM : 871
rocksdb.manifest.file.sync.micros P50 : 409.000000 P95 : 409.000000 P99 : 409.000000 P100 : 409.000000 COUNT : 1 SUM : 409
rocksdb.table.open.io.micros P50 : 92.000000 P95 : 92.000000 P99 : 92.000000 P100 : 92.000000 COUNT : 2 SUM : 177
rocksdb.db.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.compaction.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.block.get.micros P50 : 0.750000 P95 : 19.900000 P99 : 21.000000 P100 : 21.000000 COUNT : 6 SUM : 38
rocksdb.write.raw.block.micros P50 : 0.714286 P95 : 2.750000 P99 : 2.950000 P100 : 3.000000 COUNT : 10 SUM : 12
rocksdb.l0.slowdown.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.memtable.compaction.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.files.stall.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.hard.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.soft.rate.limit.delay.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.numfiles.in.singlecompaction P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.write.stall P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.read.micros P50 : 11.250000 P95 : 14.625000 P99 : 14.925000 P100 : 15.000000 COUNT : 6 SUM : 69
rocksdb.num.subcompactions.scheduled P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.per.read P50 : 34.000000 P95 : 34.000000 P99 : 34.000000 P100 : 34.000000 COUNT : 3 SUM : 102
rocksdb.bytes.per.write P50 : 51.000000 P95 : 103.000000 P99 : 103.000000 P100 : 103.000000 COUNT : 2 SUM : 145
rocksdb.bytes.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.compressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.bytes.decompressed P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.compression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.decompression.times.nanos P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.read.num.merge_operands P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.key.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.value.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.get.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.multiget.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.seek.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.next.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.prev.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.write.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.read.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.blob.file.sync.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.gc.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.compression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.blobdb.decompression.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.db.flush.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.sst.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.index.and.filter.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.data.blocks.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.sst.read.per.level P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.error.handler.autoresume.retry.count P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.read.bytes P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.poll.wait.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.prefetched.bytes.discarded P50 : 1032.000000 P95 : 1032.000000 P99 : 1032.000000 P100 : 1032.000000 COUNT : 2 SUM : 2058
rocksdb.multiget.io.batch.size P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.num.level.read.per.multiget P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
rocksdb.async.prefetch.abort.micros P50 : 0.000000 P95 : 0.000000 P99 : 0.000000 P100 : 0.000000 COUNT : 0 SUM : 0
2025/06/19-19:28:00.200279 3edc [db\db_impl\db_impl.cc:927] ------- PERSISTING STATS -------
2025/06/19-19:28:00.200303 3edc [db\db_impl\db_impl.cc:997] [Pre-GC] In-memory stats history size: 16 bytes, slice count: 0
2025/06/19-19:28:00.200306 3edc [db\db_impl\db_impl.cc:1006] [Post-GC] In-memory stats history size: 16 bytes, slice count: 0
