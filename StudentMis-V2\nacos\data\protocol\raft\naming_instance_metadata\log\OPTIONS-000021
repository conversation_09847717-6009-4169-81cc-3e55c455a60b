# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=7.7.3
  options_file_version=1.1

[DBOptions]
  delayed_write_rate=16777216
  delete_obsolete_files_period_micros=21600000000
  writable_file_max_buffer_size=1048576
  max_background_compactions=4
  max_background_jobs=2
  max_subcompactions=1
  avoid_flush_during_shutdown=false
  max_total_wal_size=1073741824
  stats_dump_period_sec=600
  max_background_flushes=1
  stats_persist_period_sec=600
  stats_history_buffer_size=1048576
  max_open_files=-1
  bytes_per_sync=0
  wal_bytes_per_sync=0
  strict_bytes_per_sync=false
  compaction_readahead_size=0
  allow_fallocate=true
  advise_random_on_open=true
  dump_malloc_stats=false
  track_and_verify_wals_in_manifest=false
  WAL_ttl_seconds=0
  use_direct_reads=false
  allow_2pc=false
  allow_mmap_reads=false
  random_access_max_buffer_size=1048576
  allow_mmap_writes=false
  wal_compression=kNoCompression
  two_write_queues=false
  use_direct_io_for_flush_and_compaction=false
  skip_stats_update_on_db_open=false
  fail_if_options_file_error=false
  max_manifest_file_size=1073741824
  wal_filter=nullptr
  create_if_missing=true
  error_if_exists=false
  create_missing_column_families=true
  enable_thread_tracking=false
  use_fsync=false
  log_file_time_to_roll=0
  keep_log_file_num=100
  is_fd_close_on_exec=true
  paranoid_checks=true
  flush_verify_memtable_count=true
  info_log_level=INFO_LEVEL
  verify_sst_unique_id_in_manifest=true
  skip_checking_sst_file_sizes_on_db_open=false
  enable_pipelined_write=false
  use_adaptive_mutex=false
  max_log_file_size=0
  max_file_opening_threads=16
  table_cache_numshardbits=6
  max_write_batch_group_size_bytes=1048576
  db_write_buffer_size=0
  recycle_log_file_num=0
  manifest_preallocation_size=4194304
  write_thread_slow_yield_usec=3
  unordered_write=false
  WAL_size_limit_MB=0
  persist_stats_to_disk=false
  allow_concurrent_memtable_write=true
  wal_recovery_mode=kPointInTimeRecovery
  enable_write_thread_adaptive_yield=true
  write_thread_max_yield_usec=100
  access_hint_on_compaction_start=NORMAL
  avoid_flush_during_recovery=false
  allow_ingest_behind=false
  manual_wal_flush=false
  atomic_flush=false
  enforce_single_del_contracts=true
  avoid_unnecessary_blocking_io=false
  write_dbid_to_manifest=false
  log_readahead_size=0
  best_efforts_recovery=false
  max_bgerror_resume_count=2147483647
  bgerror_resume_retry_interval=1000000
  db_host_id=__hostname__
  allow_data_in_errors=false
  file_checksum_gen_factory=nullptr
  lowest_used_cache_tier=kNonVolatileBlockTier
  

[CFOptions "default"]
  blob_compaction_readahead_size=0
  blob_compression_type=kNoCompression
  hard_pending_compaction_bytes_limit=274877906944
  level0_file_num_compaction_trigger=10
  experimental_mempurge_threshold=0.000000
  max_bytes_for_level_base=536870912
  report_bg_io_stats=false
  max_bytes_for_level_multiplier=10.000000
  disable_auto_compactions=false
  check_flush_compaction_key_order=true
  enable_blob_files=false
  paranoid_file_checks=false
  blob_file_starting_level=0
  blob_file_size=268435456
  soft_pending_compaction_bytes_limit=68719476736
  bottommost_compression_opts={enabled=false;max_dict_bytes=0;window_bits=-14;level=32767;parallel_threads=1;strategy=0;zstd_max_train_bytes=0;max_dict_buffer_bytes=0;use_zstd_dict_trainer=true;}
  max_compaction_bytes=1677721600
  max_sequential_skip_in_iterations=8
  level0_slowdown_writes_trigger=20
  level0_stop_writes_trigger=40
  max_write_buffer_number=3
  target_file_size_multiplier=1
  prefix_extractor=rocksdb.FixedPrefix.8
  arena_block_size=1048576
  prepopulate_blob_cache=kDisable
  inplace_update_num_locks=10000
  max_successive_merges=0
  memtable_huge_page_size=0
  write_buffer_size=67108864
  enable_blob_garbage_collection=false
  memtable_prefix_bloom_size_ratio=0.125000
  memtable_whole_key_filtering=false
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  target_file_size_base=67108864
  min_blob_size=0
  compression=kSnappyCompression
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;age_for_warm=0;}
  compaction_options_universal={allow_trivial_move=false;max_size_amplification_percent=200;size_ratio=1;incremental=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_merge_width=4294967295;}
  ttl=2592000
  periodic_compaction_seconds=0
  last_level_temperature=kUnknown
  blob_garbage_collection_age_cutoff=0.250000
  blob_garbage_collection_force_threshold=1.000000
  sample_for_compression=0
  bottommost_compression=kDisableCompressionOption
  memtable_protection_bytes_per_key=0
  compression_opts={enabled=false;max_dict_bytes=0;window_bits=-14;level=32767;parallel_threads=1;strategy=0;zstd_max_train_bytes=0;max_dict_buffer_bytes=0;use_zstd_dict_trainer=true;}
  merge_operator={id=StringAppendOperator;delimiter=,;}
  preclude_last_level_data_seconds=0
  level_compaction_dynamic_level_bytes=false
  num_levels=7
  inplace_update_support=false
  min_write_buffer_number_to_merge=1
  optimize_filters_for_hits=false
  force_consistency_checks=true
  max_write_buffer_number_to_maintain=0
  max_write_buffer_size_to_maintain=0
  bloom_locality=0
  comparator=leveldb.BytewiseComparator
  memtable_insert_with_hint_prefix_extractor=nullptr
  memtable_factory=SkipListFactory
  table_factory=BlockBasedTable
  compaction_filter=nullptr
  compaction_filter_factory=nullptr
  compaction_style=kCompactionStyleLevel
  compaction_pri=kMinOverlappingRatio
  sst_partitioner_factory=nullptr
  
[TableOptions/BlockBasedTable "default"]
  pin_top_level_index_and_filter=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  cache_index_and_filter_blocks=false
  cache_index_and_filter_blocks_with_high_priority=true
  index_shortening=kShortenSeparators
  pin_l0_filter_and_index_blocks_in_cache=true
  index_type=kTwoLevelIndexSearch
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  checksum=kCRC32c
  no_block_cache=false
  block_size=4096
  block_size_deviation=10
  block_restart_interval=16
  index_block_restart_interval=1
  metadata_block_size=8192
  partition_filters=true
  optimize_filters_for_memory=false
  filter_policy=nullptr
  whole_key_filtering=true
  verify_compression=false
  detect_filter_construct_corruption=false
  num_file_reads_for_auto_readahead=2
  format_version=5
  read_amp_bytes_per_bit=0
  block_align=false
  enable_index_compression=true
  metadata_cache_options={top_level_index_pinning=kFallback;unpartitioned_pinning=kFallback;partition_pinning=kFallback;}
  max_auto_readahead_size=262144
  prepopulate_block_cache=kDisable
  initial_auto_readahead_size=8192
  

[CFOptions "Configuration"]
  blob_compaction_readahead_size=0
  blob_compression_type=kNoCompression
  hard_pending_compaction_bytes_limit=274877906944
  level0_file_num_compaction_trigger=10
  experimental_mempurge_threshold=0.000000
  max_bytes_for_level_base=536870912
  report_bg_io_stats=false
  max_bytes_for_level_multiplier=10.000000
  disable_auto_compactions=false
  check_flush_compaction_key_order=true
  enable_blob_files=false
  paranoid_file_checks=false
  blob_file_starting_level=0
  blob_file_size=268435456
  soft_pending_compaction_bytes_limit=68719476736
  bottommost_compression_opts={enabled=false;max_dict_bytes=0;window_bits=-14;level=32767;parallel_threads=1;strategy=0;zstd_max_train_bytes=0;max_dict_buffer_bytes=0;use_zstd_dict_trainer=true;}
  max_compaction_bytes=1677721600
  max_sequential_skip_in_iterations=8
  level0_slowdown_writes_trigger=20
  level0_stop_writes_trigger=40
  max_write_buffer_number=3
  target_file_size_multiplier=1
  prefix_extractor=rocksdb.FixedPrefix.8
  arena_block_size=1048576
  prepopulate_blob_cache=kDisable
  inplace_update_num_locks=10000
  max_successive_merges=0
  memtable_huge_page_size=0
  write_buffer_size=67108864
  enable_blob_garbage_collection=false
  memtable_prefix_bloom_size_ratio=0.125000
  memtable_whole_key_filtering=false
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  target_file_size_base=67108864
  min_blob_size=0
  compression=kSnappyCompression
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;age_for_warm=0;}
  compaction_options_universal={allow_trivial_move=false;max_size_amplification_percent=200;size_ratio=1;incremental=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_merge_width=4294967295;}
  ttl=2592000
  periodic_compaction_seconds=0
  last_level_temperature=kUnknown
  blob_garbage_collection_age_cutoff=0.250000
  blob_garbage_collection_force_threshold=1.000000
  sample_for_compression=0
  bottommost_compression=kDisableCompressionOption
  memtable_protection_bytes_per_key=0
  compression_opts={enabled=false;max_dict_bytes=0;window_bits=-14;level=32767;parallel_threads=1;strategy=0;zstd_max_train_bytes=0;max_dict_buffer_bytes=0;use_zstd_dict_trainer=true;}
  merge_operator={id=StringAppendOperator;delimiter=,;}
  preclude_last_level_data_seconds=0
  level_compaction_dynamic_level_bytes=false
  num_levels=7
  inplace_update_support=false
  min_write_buffer_number_to_merge=1
  optimize_filters_for_hits=false
  force_consistency_checks=true
  max_write_buffer_number_to_maintain=0
  max_write_buffer_size_to_maintain=0
  bloom_locality=0
  comparator=leveldb.BytewiseComparator
  memtable_insert_with_hint_prefix_extractor=nullptr
  memtable_factory=SkipListFactory
  table_factory=BlockBasedTable
  compaction_filter=nullptr
  compaction_filter_factory=nullptr
  compaction_style=kCompactionStyleLevel
  compaction_pri=kMinOverlappingRatio
  sst_partitioner_factory=nullptr
  
[TableOptions/BlockBasedTable "Configuration"]
  pin_top_level_index_and_filter=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  cache_index_and_filter_blocks=false
  cache_index_and_filter_blocks_with_high_priority=true
  index_shortening=kShortenSeparators
  pin_l0_filter_and_index_blocks_in_cache=true
  index_type=kTwoLevelIndexSearch
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  checksum=kCRC32c
  no_block_cache=false
  block_size=4096
  block_size_deviation=10
  block_restart_interval=16
  index_block_restart_interval=1
  metadata_block_size=8192
  partition_filters=true
  optimize_filters_for_memory=false
  filter_policy=nullptr
  whole_key_filtering=true
  verify_compression=false
  detect_filter_construct_corruption=false
  num_file_reads_for_auto_readahead=2
  format_version=5
  read_amp_bytes_per_bit=0
  block_align=false
  enable_index_compression=true
  metadata_cache_options={top_level_index_pinning=kFallback;unpartitioned_pinning=kFallback;partition_pinning=kFallback;}
  max_auto_readahead_size=262144
  prepopulate_block_cache=kDisable
  initial_auto_readahead_size=8192
  
