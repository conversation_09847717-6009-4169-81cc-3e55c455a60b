package edu.tsinghua.studentmis.analytics.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习行为分析请求DTO
 * 
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Schema(description = "学习行为分析请求")
public class LearningBehaviorAnalysisRequest {

    @Schema(description = "学生ID", required = true)
    @NotNull(message = "学生ID不能为空")
    private Long studentId;

    @Schema(description = "分析开始时间")
    private LocalDateTime startTime;

    @Schema(description = "分析结束时间")
    private LocalDateTime endTime;

    @Schema(description = "学期ID")
    private Long semesterId;

    @Schema(description = "课程ID列表")
    private List<Long> courseIds;

    @Schema(description = "分析类型", allowableValues = {"ATTENDANCE", "ASSIGNMENT", "EXAM", "OVERALL"})
    private String analysisType;

    @Schema(description = "分析维度", allowableValues = {"DAILY", "WEEKLY", "MONTHLY", "SEMESTER"})
    private String analysisDimension;

    @Schema(description = "是否包含预测分析")
    private Boolean includePrediction = false;

    @Schema(description = "是否包含对比分析")
    private Boolean includeComparison = false;

    @Schema(description = "对比学生ID列表")
    private List<Long> comparisonStudentIds;

    @Schema(description = "分析深度", allowableValues = {"BASIC", "DETAILED", "COMPREHENSIVE"})
    private String analysisDepth = "BASIC";

    /**
     * 分析类型枚举
     */
    public enum AnalysisType {
        ATTENDANCE("出勤分析"),
        ASSIGNMENT("作业分析"),
        EXAM("考试分析"),
        OVERALL("综合分析");

        private final String description;

        AnalysisType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 分析维度枚举
     */
    public enum AnalysisDimension {
        DAILY("日度分析"),
        WEEKLY("周度分析"),
        MONTHLY("月度分析"),
        SEMESTER("学期分析");

        private final String description;

        AnalysisDimension(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 分析深度枚举
     */
    public enum AnalysisDepth {
        BASIC("基础分析"),
        DETAILED("详细分析"),
        COMPREHENSIVE("综合分析");

        private final String description;

        AnalysisDepth(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
