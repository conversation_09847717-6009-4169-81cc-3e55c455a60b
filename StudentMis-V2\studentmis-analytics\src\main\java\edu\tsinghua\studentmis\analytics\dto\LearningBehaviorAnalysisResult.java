package edu.tsinghua.studentmis.analytics.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习行为分析结果DTO
 *
 * <AUTHOR> Team
 * @since 2.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学习行为分析结果")
public class LearningBehaviorAnalysisResult {

    @Schema(description = "学生ID")
    private Long studentId;

    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学期ID")
    private Long semesterId;

    @Schema(description = "分析时间")
    private LocalDateTime analysisTime;

    @Schema(description = "分析日期")
    private LocalDate analysisDate;

    @Schema(description = "分析类型")
    private String analysisType;

    @Schema(description = "分析维度")
    private String analysisDimension;

    @Schema(description = "总体评分")
    private BigDecimal overallScore;

    @Schema(description = "学习活跃度")
    private BigDecimal learningActivity;

    @Schema(description = "出勤率")
    private BigDecimal attendanceRate;

    @Schema(description = "作业完成率")
    private BigDecimal assignmentCompletionRate;

    @Schema(description = "平均成绩")
    private BigDecimal averageGrade;

    // Service期望的字段
    @Schema(description = "登录频次")
    private Integer loginFrequency;

    @Schema(description = "学习时长")
    private Integer studyDuration;

    @Schema(description = "论坛参与次数")
    private Integer forumParticipation;

    @Schema(description = "资源访问次数")
    private Integer resourceAccessCount;

    @Schema(description = "测验尝试次数")
    private Integer quizAttempts;

    @Schema(description = "求助频次")
    private Integer helpSeekingFrequency;

    @Schema(description = "同伴互动得分")
    private BigDecimal peerInteractionScore;

    @Schema(description = "学习模式")
    private String learningPattern;

    @Schema(description = "参与度等级")
    private String engagementLevel;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "改进建议")
    private List<String> improvementSuggestions;

    @Schema(description = "学习时长统计")
    private Map<String, Object> studyTimeStats;

    @Schema(description = "行为模式分析")
    private List<BehaviorPattern> behaviorPatterns;

    @Schema(description = "学习建议")
    private List<String> recommendations;

    @Schema(description = "预测结果")
    private PredictionResult prediction;

    @Schema(description = "对比分析结果")
    private ComparisonResult comparison;

    @Schema(description = "详细数据")
    private Map<String, Object> detailData;

    /**
     * 行为模式
     */
    @Data
    @Builder
    @Schema(description = "行为模式")
    public static class BehaviorPattern {
        
        @Schema(description = "模式名称")
        private String patternName;

        @Schema(description = "模式描述")
        private String description;

        @Schema(description = "置信度")
        private BigDecimal confidence;

        @Schema(description = "频率")
        private Integer frequency;

        @Schema(description = "影响因子")
        private BigDecimal impactFactor;
    }

    /**
     * 预测结果
     */
    @Data
    @Builder
    @Schema(description = "预测结果")
    public static class PredictionResult {
        
        @Schema(description = "预测成绩")
        private BigDecimal predictedGrade;

        @Schema(description = "预测准确度")
        private BigDecimal accuracy;

        @Schema(description = "风险等级")
        private String riskLevel;

        @Schema(description = "预测建议")
        private List<String> suggestions;
    }

    /**
     * 对比分析结果
     */
    @Data
    @Builder
    @Schema(description = "对比分析结果")
    public static class ComparisonResult {
        
        @Schema(description = "排名")
        private Integer ranking;

        @Schema(description = "百分位")
        private BigDecimal percentile;

        @Schema(description = "优势领域")
        private List<String> strengths;

        @Schema(description = "改进领域")
        private List<String> improvements;

        @Schema(description = "对比数据")
        private Map<String, Object> comparisonData;
    }
}
