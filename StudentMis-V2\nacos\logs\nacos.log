2025-06-19 19:26:04,863 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 5236 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\bin)

2025-06-19 19:26:04,864 INFO The following 1 profile is active: "standalone"

2025-06-19 19:26:04,996 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 19:26:04,997 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 19:26:05,223 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 19:26:05,333 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 19:26:05,344 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 19:26:05,355 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,356 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,357 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,358 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,359 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,360 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,361 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:26:05,646 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 19:26:07,519 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 19:26:07,721 INFO Starting service [Tomcat]

2025-06-19 19:26:07,722 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 19:26:07,831 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 19:26:07,831 INFO Root WebApplicationContext: initialization completed in 2910 ms

2025-06-19 19:26:08,098 INFO Nacos-related cluster resource initialization

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,106 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:26:08,109 INFO The cluster resource is initialized

2025-06-19 19:26:08,475 INFO HikariPool-1 - Starting...

2025-06-19 19:26:08,483 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 19:26:09,179 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 19:26:09,182 INFO HikariPool-1 - Start completed.

2025-06-19 19:26:10,109 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 19:26:10,110 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 19:26:10,232 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 19:26:10,299 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 19:26:10,311 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 19:26:10,319 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 19:26:10,426 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 19:26:10,505 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 19:26:10,507 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@5a6d30e2, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@a098d76]

2025-06-19 19:26:10,508 INFO No connection rule content found ,use default empty rule 

2025-06-19 19:26:10,511 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,518 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 19:26:10,518 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,519 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 19:26:10,519 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 19:26:10,559 INFO Ready to get current node abilities...

2025-06-19 19:26:10,562 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT, SERVER, CLUSTER_CLIENT]

2025-06-19 19:26:10,562 INFO Initialize current abilities finish...

2025-06-19 19:26:10,563 INFO Ready to get current node abilities...

2025-06-19 19:26:10,564 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 19:26:10,564 INFO Initialize current abilities finish...

2025-06-19 19:26:10,564 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 19:26:11,442 INFO Connection check task start

2025-06-19 19:26:11,448 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:11,448 INFO Out dated connection ,size=0

2025-06-19 19:26:11,448 INFO Connection check task end

2025-06-19 19:26:12,398 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 19:26:12,843 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 19:26:12,844 INFO Will not secure Ant [pattern='/**']

2025-06-19 19:26:12,871 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@60f77af, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2574a9e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d4d8579, org.springframework.security.web.header.HeaderWriterFilter@6f9e08d4, org.springframework.security.web.csrf.CsrfFilter@1f992a3a, org.springframework.security.web.authentication.logout.LogoutFilter@412c5e8b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@29bcf51d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3b24087d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@18b6d3c1, org.springframework.security.web.session.SessionManagementFilter@2b08772d, org.springframework.security.web.access.ExceptionTranslationFilter@30bf26df]

2025-06-19 19:26:12,909 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 19:26:12,964 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 19:26:12,982 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 19:26:12,982 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,982 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 19:26:12,982 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 19:26:12,983 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 19:26:12,983 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,983 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 19:26:12,984 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,984 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 19:26:12,984 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigPublish found  

2025-06-19 19:26:12,985 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 19:26:12,985 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,985 INFO No tps control rule of ConfigQuery found  

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,986 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 19:26:12,987 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigListen found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of ConfigRemove found  

2025-06-19 19:26:12,987 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,987 INFO No tps control rule of HealthCheck found  

2025-06-19 19:26:12,987 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,988 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 19:26:12,988 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,989 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:26:12,993 INFO Started Nacos in 9.176 seconds (JVM running for 9.67)

2025-06-19 19:26:12,994 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 19:26:13,514 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:14,459 INFO Connection check task start

2025-06-19 19:26:14,459 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:14,459 INFO Out dated connection ,size=0

2025-06-19 19:26:14,459 INFO Connection check task end

2025-06-19 19:26:16,524 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:17,472 INFO Connection check task start

2025-06-19 19:26:17,472 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:17,472 INFO Out dated connection ,size=0

2025-06-19 19:26:17,472 INFO Connection check task end

2025-06-19 19:26:19,540 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:20,484 INFO Connection check task start

2025-06-19 19:26:20,484 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:20,484 INFO Out dated connection ,size=0

2025-06-19 19:26:20,484 INFO Connection check task end

2025-06-19 19:26:22,544 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:23,497 INFO Connection check task start

2025-06-19 19:26:23,497 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:23,497 INFO Out dated connection ,size=0

2025-06-19 19:26:23,497 INFO Connection check task end

2025-06-19 19:26:25,558 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:26,506 INFO Connection check task start

2025-06-19 19:26:26,506 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:26,506 INFO Out dated connection ,size=0

2025-06-19 19:26:26,506 INFO Connection check task end

2025-06-19 19:26:28,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:29,515 INFO Connection check task start

2025-06-19 19:26:29,515 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:29,515 INFO Out dated connection ,size=0

2025-06-19 19:26:29,515 INFO Connection check task end

2025-06-19 19:26:31,569 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:32,525 INFO Connection check task start

2025-06-19 19:26:32,525 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:32,525 INFO Out dated connection ,size=0

2025-06-19 19:26:32,525 INFO Connection check task end

2025-06-19 19:26:34,575 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:35,539 INFO Connection check task start

2025-06-19 19:26:35,539 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:35,539 INFO Out dated connection ,size=0

2025-06-19 19:26:35,539 INFO Connection check task end

2025-06-19 19:26:37,586 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:38,547 INFO Connection check task start

2025-06-19 19:26:38,547 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:38,548 INFO Out dated connection ,size=0

2025-06-19 19:26:38,548 INFO Connection check task end

2025-06-19 19:26:40,594 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:41,553 INFO Connection check task start

2025-06-19 19:26:41,553 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:41,553 INFO Out dated connection ,size=0

2025-06-19 19:26:41,553 INFO Connection check task end

2025-06-19 19:26:43,604 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:44,567 INFO Connection check task start

2025-06-19 19:26:44,567 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:44,567 INFO Out dated connection ,size=0

2025-06-19 19:26:44,567 INFO Connection check task end

2025-06-19 19:26:46,617 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:47,568 INFO Connection check task start

2025-06-19 19:26:47,568 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:47,568 INFO Out dated connection ,size=0

2025-06-19 19:26:47,568 INFO Connection check task end

2025-06-19 19:26:49,625 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:50,573 INFO Connection check task start

2025-06-19 19:26:50,573 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:50,573 INFO Out dated connection ,size=0

2025-06-19 19:26:50,573 INFO Connection check task end

2025-06-19 19:26:52,634 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:53,588 INFO Connection check task start

2025-06-19 19:26:53,588 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:53,588 INFO Out dated connection ,size=0

2025-06-19 19:26:53,588 INFO Connection check task end

2025-06-19 19:26:55,639 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:56,593 INFO Connection check task start

2025-06-19 19:26:56,593 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:56,593 INFO Out dated connection ,size=0

2025-06-19 19:26:56,593 INFO Connection check task end

2025-06-19 19:26:58,651 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:26:59,596 INFO Connection check task start

2025-06-19 19:26:59,596 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:26:59,596 INFO Out dated connection ,size=0

2025-06-19 19:26:59,596 INFO Connection check task end

2025-06-19 19:27:01,663 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:02,612 INFO Connection check task start

2025-06-19 19:27:02,612 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:02,612 INFO Out dated connection ,size=0

2025-06-19 19:27:02,612 INFO Connection check task end

2025-06-19 19:27:04,670 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:05,629 INFO Connection check task start

2025-06-19 19:27:05,629 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:05,629 INFO Out dated connection ,size=0

2025-06-19 19:27:05,629 INFO Connection check task end

2025-06-19 19:27:07,677 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:08,632 INFO Connection check task start

2025-06-19 19:27:08,632 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:08,632 INFO Out dated connection ,size=0

2025-06-19 19:27:08,632 INFO Connection check task end

2025-06-19 19:27:10,690 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:11,639 INFO Connection check task start

2025-06-19 19:27:11,639 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:11,639 INFO Out dated connection ,size=0

2025-06-19 19:27:11,639 INFO Connection check task end

2025-06-19 19:27:13,691 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:14,649 INFO Connection check task start

2025-06-19 19:27:14,649 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:14,649 INFO Out dated connection ,size=0

2025-06-19 19:27:14,649 INFO Connection check task end

2025-06-19 19:27:16,692 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:17,663 INFO Connection check task start

2025-06-19 19:27:17,663 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:17,663 INFO Out dated connection ,size=0

2025-06-19 19:27:17,663 INFO Connection check task end

2025-06-19 19:27:49,724 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 9160 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\bin)

2025-06-19 19:27:49,725 INFO The following 1 profile is active: "standalone"

2025-06-19 19:27:49,862 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 19:27:49,862 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 19:27:49,863 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 19:27:50,060 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 19:27:50,162 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 19:27:50,174 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 19:27:50,185 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,186 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,187 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,188 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,189 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,190 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,191 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 19:27:50,477 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 19:27:52,397 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 19:27:52,597 INFO Starting service [Tomcat]

2025-06-19 19:27:52,597 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 19:27:52,708 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 19:27:52,708 INFO Root WebApplicationContext: initialization completed in 2922 ms

2025-06-19 19:27:52,969 INFO Nacos-related cluster resource initialization

2025-06-19 19:27:52,976 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:27:52,977 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 19:27:52,979 INFO The cluster resource is initialized

2025-06-19 19:27:53,364 INFO HikariPool-1 - Starting...

2025-06-19 19:27:53,372 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 19:27:53,785 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 19:27:53,787 INFO HikariPool-1 - Start completed.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 19:27:54,709 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 19:27:54,710 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 19:27:54,830 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 19:27:54,884 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 19:27:54,895 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 19:27:54,903 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 19:27:55,016 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 19:27:55,114 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 19:27:55,116 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@cb7fa71, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@4b6e1c0]

2025-06-19 19:27:55,117 INFO No connection rule content found ,use default empty rule 

2025-06-19 19:27:55,121 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:55,129 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,130 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,130 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 19:27:55,130 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 19:27:55,168 INFO Ready to get current node abilities...

2025-06-19 19:27:55,171 INFO Ready to initialize current node abilities, support modes: [CLUSTER_CLIENT, SDK_CLIENT, SERVER]

2025-06-19 19:27:55,172 INFO Initialize current abilities finish...

2025-06-19 19:27:55,173 INFO Ready to get current node abilities...

2025-06-19 19:27:55,173 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 19:27:55,173 INFO Initialize current abilities finish...

2025-06-19 19:27:55,174 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 19:27:56,033 INFO Connection check task start

2025-06-19 19:27:56,038 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:56,038 INFO Out dated connection ,size=0

2025-06-19 19:27:56,038 INFO Connection check task end

2025-06-19 19:27:56,859 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 19:27:57,328 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 19:27:57,329 INFO Will not secure Ant [pattern='/**']

2025-06-19 19:27:57,352 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@32091c14, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c40ffef, org.springframework.security.web.context.SecurityContextPersistenceFilter@3bfae028, org.springframework.security.web.header.HeaderWriterFilter@74b86971, org.springframework.security.web.csrf.CsrfFilter@1e5eb20a, org.springframework.security.web.authentication.logout.LogoutFilter@4391a2d8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47829d6d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7a1b8a46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@286855ea, org.springframework.security.web.session.SessionManagementFilter@1ca610a0, org.springframework.security.web.access.ExceptionTranslationFilter@4538856f]

2025-06-19 19:27:57,381 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 19:27:57,424 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 19:27:57,440 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 19:27:57,440 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,440 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 19:27:57,441 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 19:27:57,441 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,441 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 19:27:57,441 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 19:27:57,442 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,442 INFO No tps control rule of ConfigQuery found  

2025-06-19 19:27:57,442 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,443 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 INFO No tps control rule of ConfigPublish found  

2025-06-19 19:27:57,444 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,444 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 19:27:57,444 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of ConfigListen found  

2025-06-19 19:27:57,445 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of ConfigRemove found  

2025-06-19 19:27:57,445 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of HealthCheck found  

2025-06-19 19:27:57,445 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,445 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 19:27:57,445 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,446 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 19:27:57,450 INFO Started Nacos in 8.783 seconds (JVM running for 9.26)

2025-06-19 19:27:57,450 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 19:27:58,135 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:27:59,052 INFO Connection check task start

2025-06-19 19:27:59,052 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:27:59,052 INFO Out dated connection ,size=0

2025-06-19 19:27:59,052 INFO Connection check task end

2025-06-19 19:28:01,136 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:02,052 INFO Connection check task start

2025-06-19 19:28:02,052 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:02,052 INFO Out dated connection ,size=0

2025-06-19 19:28:02,052 INFO Connection check task end

2025-06-19 19:28:04,139 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:05,069 INFO Connection check task start

2025-06-19 19:28:05,069 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:05,069 INFO Out dated connection ,size=0

2025-06-19 19:28:05,069 INFO Connection check task end

2025-06-19 19:28:07,149 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:08,082 INFO Connection check task start

2025-06-19 19:28:08,082 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:08,082 INFO Out dated connection ,size=0

2025-06-19 19:28:08,082 INFO Connection check task end

2025-06-19 19:28:10,163 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:11,093 INFO Connection check task start

2025-06-19 19:28:11,093 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:11,093 INFO Out dated connection ,size=0

2025-06-19 19:28:11,093 INFO Connection check task end

2025-06-19 19:28:13,164 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:14,104 INFO Connection check task start

2025-06-19 19:28:14,104 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:14,104 INFO Out dated connection ,size=0

2025-06-19 19:28:14,104 INFO Connection check task end

2025-06-19 19:28:16,169 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:17,117 INFO Connection check task start

2025-06-19 19:28:17,117 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:17,117 INFO Out dated connection ,size=0

2025-06-19 19:28:17,117 INFO Connection check task end

2025-06-19 19:28:19,180 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:20,119 INFO Connection check task start

2025-06-19 19:28:20,119 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:20,119 INFO Out dated connection ,size=0

2025-06-19 19:28:20,119 INFO Connection check task end

2025-06-19 19:28:22,189 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:23,127 INFO Connection check task start

2025-06-19 19:28:23,127 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:23,127 INFO Out dated connection ,size=0

2025-06-19 19:28:23,127 INFO Connection check task end

2025-06-19 19:28:25,202 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:26,135 INFO Connection check task start

2025-06-19 19:28:26,135 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:26,135 INFO Out dated connection ,size=0

2025-06-19 19:28:26,135 INFO Connection check task end

2025-06-19 19:28:28,204 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:29,136 INFO Connection check task start

2025-06-19 19:28:29,136 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:29,136 INFO Out dated connection ,size=0

2025-06-19 19:28:29,136 INFO Connection check task end

2025-06-19 19:28:31,212 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:32,144 INFO Connection check task start

2025-06-19 19:28:32,145 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:32,145 INFO Out dated connection ,size=0

2025-06-19 19:28:32,145 INFO Connection check task end

2025-06-19 19:28:34,217 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:35,155 INFO Connection check task start

2025-06-19 19:28:35,155 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:35,155 INFO Out dated connection ,size=0

2025-06-19 19:28:35,155 INFO Connection check task end

2025-06-19 19:28:37,223 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:38,165 INFO Connection check task start

2025-06-19 19:28:38,165 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:38,165 INFO Out dated connection ,size=0

2025-06-19 19:28:38,165 INFO Connection check task end

2025-06-19 19:28:40,225 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:41,172 INFO Connection check task start

2025-06-19 19:28:41,172 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:41,172 INFO Out dated connection ,size=0

2025-06-19 19:28:41,172 INFO Connection check task end

2025-06-19 19:28:43,239 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:44,175 INFO Connection check task start

2025-06-19 19:28:44,175 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:44,175 INFO Out dated connection ,size=0

2025-06-19 19:28:44,175 INFO Connection check task end

2025-06-19 19:28:46,241 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:47,185 INFO Connection check task start

2025-06-19 19:28:47,185 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:47,185 INFO Out dated connection ,size=0

2025-06-19 19:28:47,185 INFO Connection check task end

2025-06-19 19:28:49,253 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:50,197 INFO Connection check task start

2025-06-19 19:28:50,197 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:50,197 INFO Out dated connection ,size=0

2025-06-19 19:28:50,197 INFO Connection check task end

2025-06-19 19:28:52,254 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:53,203 INFO Connection check task start

2025-06-19 19:28:53,203 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:53,203 INFO Out dated connection ,size=0

2025-06-19 19:28:53,203 INFO Connection check task end

2025-06-19 19:28:55,267 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:56,217 INFO Connection check task start

2025-06-19 19:28:56,217 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:56,217 INFO Out dated connection ,size=0

2025-06-19 19:28:56,217 INFO Connection check task end

2025-06-19 19:28:58,270 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:28:59,219 INFO Connection check task start

2025-06-19 19:28:59,219 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:28:59,219 INFO Out dated connection ,size=0

2025-06-19 19:28:59,219 INFO Connection check task end

2025-06-19 19:29:01,285 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:02,220 INFO Connection check task start

2025-06-19 19:29:02,220 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:02,220 INFO Out dated connection ,size=0

2025-06-19 19:29:02,220 INFO Connection check task end

2025-06-19 19:29:04,288 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:05,221 INFO Connection check task start

2025-06-19 19:29:05,221 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:05,221 INFO Out dated connection ,size=0

2025-06-19 19:29:05,221 INFO Connection check task end

2025-06-19 19:29:07,290 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:08,223 INFO Connection check task start

2025-06-19 19:29:08,223 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:08,223 INFO Out dated connection ,size=0

2025-06-19 19:29:08,223 INFO Connection check task end

2025-06-19 19:29:10,304 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:11,235 INFO Connection check task start

2025-06-19 19:29:11,235 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:11,235 INFO Out dated connection ,size=0

2025-06-19 19:29:11,235 INFO Connection check task end

2025-06-19 19:29:13,309 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:14,244 INFO Connection check task start

2025-06-19 19:29:14,244 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:14,244 INFO Out dated connection ,size=0

2025-06-19 19:29:14,244 INFO Connection check task end

2025-06-19 19:29:16,318 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:17,260 INFO Connection check task start

2025-06-19 19:29:17,260 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:17,260 INFO Out dated connection ,size=0

2025-06-19 19:29:17,261 INFO Connection check task end

2025-06-19 19:29:19,328 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:20,272 INFO Connection check task start

2025-06-19 19:29:20,272 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:20,272 INFO Out dated connection ,size=0

2025-06-19 19:29:20,272 INFO Connection check task end

2025-06-19 19:29:22,334 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:23,281 INFO Connection check task start

2025-06-19 19:29:23,281 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:23,281 INFO Out dated connection ,size=0

2025-06-19 19:29:23,281 INFO Connection check task end

2025-06-19 19:29:25,336 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:26,284 INFO Connection check task start

2025-06-19 19:29:26,284 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:26,284 INFO Out dated connection ,size=0

2025-06-19 19:29:26,284 INFO Connection check task end

2025-06-19 19:29:28,352 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:29,289 INFO Connection check task start

2025-06-19 19:29:29,289 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:29,289 INFO Out dated connection ,size=0

2025-06-19 19:29:29,289 INFO Connection check task end

2025-06-19 19:29:31,367 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:32,301 INFO Connection check task start

2025-06-19 19:29:32,301 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:32,301 INFO Out dated connection ,size=0

2025-06-19 19:29:32,301 INFO Connection check task end

2025-06-19 19:29:34,381 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:35,313 INFO Connection check task start

2025-06-19 19:29:35,313 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:35,313 INFO Out dated connection ,size=0

2025-06-19 19:29:35,313 INFO Connection check task end

2025-06-19 19:29:37,386 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:38,320 INFO Connection check task start

2025-06-19 19:29:38,320 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:38,320 INFO Out dated connection ,size=0

2025-06-19 19:29:38,320 INFO Connection check task end

2025-06-19 19:29:40,388 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:41,334 INFO Connection check task start

2025-06-19 19:29:41,334 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:41,334 INFO Out dated connection ,size=0

2025-06-19 19:29:41,334 INFO Connection check task end

2025-06-19 19:29:43,394 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:44,345 INFO Connection check task start

2025-06-19 19:29:44,345 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:44,345 INFO Out dated connection ,size=0

2025-06-19 19:29:44,345 INFO Connection check task end

2025-06-19 19:29:46,406 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:47,352 INFO Connection check task start

2025-06-19 19:29:47,352 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:47,352 INFO Out dated connection ,size=0

2025-06-19 19:29:47,352 INFO Connection check task end

2025-06-19 19:29:49,407 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:50,363 INFO Connection check task start

2025-06-19 19:29:50,363 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:50,363 INFO Out dated connection ,size=0

2025-06-19 19:29:50,363 INFO Connection check task end

2025-06-19 19:29:52,419 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 19:29:53,374 INFO Connection check task start

2025-06-19 19:29:53,374 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 19:29:53,374 INFO Out dated connection ,size=0

2025-06-19 19:29:53,374 INFO Connection check task end

2025-06-19 20:10:56,206 INFO Starting Nacos v2.3.0 using Java 17.0.15 on DESKTOP-620EO8D with PID 21528 (D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2\nacos\target\nacos-server.jar started by jsxzxhx in D:\Users\jsxzxhx\Desktop\学生成绩管理系统\StudentMis-V2)

2025-06-19 20:10:56,207 INFO The following 1 profile is active: "standalone"

2025-06-19 20:10:56,348 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.config.server, filter com.alibaba.nacos.config.server.filter.ConfigEnabledFilter

2025-06-19 20:10:56,349 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.naming, filter com.alibaba.nacos.naming.config.NamingEnabledFilter

2025-06-19 20:10:56,349 INFO Load Nacos package exclude filter success, package prefix com.alibaba.nacos.istio, filter com.alibaba.nacos.istio.config.IstioEnabledFilter

2025-06-19 20:10:56,508 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.config.server.Config to avoid duplicate scan

2025-06-19 20:10:56,613 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.naming.NamingApp to avoid duplicate scan

2025-06-19 20:10:56,626 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.cmdb.CmdbApp to avoid duplicate scan

2025-06-19 20:10:56,638 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,638 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,639 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,640 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,640 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,640 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,640 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,640 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,641 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,641 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,641 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,641 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,642 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.istio.IstioApp to avoid duplicate scan

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,643 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,644 WARN Istio module disabled because set nacos.extension.naming.istio.enabled as false

2025-06-19 20:10:56,994 INFO Skip @SpringBootApplication annotation for class com.alibaba.nacos.prometheus.PrometheusApp to avoid duplicate scan

2025-06-19 20:10:58,816 INFO Tomcat initialized with port(s): 8848 (http)

2025-06-19 20:10:59,007 INFO Starting service [Tomcat]

2025-06-19 20:10:59,007 INFO Starting Servlet engine: [Apache Tomcat/9.0.79]

2025-06-19 20:10:59,111 INFO Initializing Spring embedded WebApplicationContext

2025-06-19 20:10:59,111 INFO Root WebApplicationContext: initialization completed in 2842 ms

2025-06-19 20:10:59,377 INFO Nacos-related cluster resource initialization

2025-06-19 20:10:59,383 INFO Load com.alibaba.nacos.core.ability.RemoteAbilityInitializer for ServerAbilityInitializer

2025-06-19 20:10:59,383 INFO Load com.alibaba.nacos.naming.ability.NamingAbilityInitializer for ServerAbilityInitializer

2025-06-19 20:10:59,386 INFO The cluster resource is initialized

2025-06-19 20:10:59,746 INFO HikariPool-1 - Starting...

2025-06-19 20:10:59,753 WARN Registered driver with driverClassName=org.apache.derby.jdbc.EmbeddedDriver was not found, trying direct instantiation.

2025-06-19 20:11:00,053 INFO HikariPool-1 - Driver does not support get/set network timeout for connections. (Feature not implemented: No details.)

2025-06-19 20:11:00,056 INFO HikariPool-1 - Start completed.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoAggrMapperByMySql) datasource(mysql) tableName(config_info_aggr) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoBetaMapperByMySql) datasource(mysql) tableName(config_info_beta) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoMapperByMySql) datasource(mysql) tableName(config_info) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigInfoTagMapperByMySql) datasource(mysql) tableName(config_info_tag) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.ConfigTagsRelationMapperByMySql) datasource(mysql) tableName(config_tags_relation) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.HistoryConfigInfoMapperByMySql) datasource(mysql) tableName(his_config_info) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantInfoMapperByMySql) datasource(mysql) tableName(tenant_info) successfully.

2025-06-19 20:11:00,846 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.TenantCapacityMapperByMySql) datasource(mysql) tableName(tenant_capacity) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.mysql.GroupCapacityMapperByMysql) datasource(mysql) tableName(group_capacity) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoAggrMapperByDerby) datasource(derby) tableName(config_info_aggr) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoBetaMapperByDerby) datasource(derby) tableName(config_info_beta) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoMapperByDerby) datasource(derby) tableName(config_info) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagMapperByDerby) datasource(derby) tableName(config_info_tag) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.ConfigInfoTagsRelationMapperByDerby) datasource(derby) tableName(config_tags_relation) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.HistoryConfigInfoMapperByDerby) datasource(derby) tableName(his_config_info) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantInfoMapperByDerby) datasource(derby) tableName(tenant_info) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.TenantCapacityMapperByDerby) datasource(derby) tableName(tenant_capacity) successfully.

2025-06-19 20:11:00,847 INFO [MapperManager] Load Mapper(class com.alibaba.nacos.plugin.datasource.impl.derby.GroupCapacityMapperByDerby) datasource(derby) tableName(group_capacity) successfully.

2025-06-19 20:11:00,964 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info

2025-06-19 20:11:01,020 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_beta

2025-06-19 20:11:01,033 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_tag

2025-06-19 20:11:01,041 INFO [MapperManager] findMapper dataSource: derby, tableName: config_info_aggr

2025-06-19 20:11:01,160 INFO Fail to find connection runtime ejector for name nacos,use default

2025-06-19 20:11:01,240 INFO Not configure type of control plugin, no limit control for current node.

2025-06-19 20:11:01,242 INFO Load connection metrics collector,size=2,[com.alibaba.nacos.config.server.service.LongPollingConnectionMetricsCollector@e57e5d6, com.alibaba.nacos.core.remote.LongConnectionMetricsCollector@78054f54]

2025-06-19 20:11:01,243 INFO No connection rule content found ,use default empty rule 

2025-06-19 20:11:01,246 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:01,251 INFO No tps control rule of CONFIG_PUSH_COUNT found  

2025-06-19 20:11:01,252 WARN Tps point for CONFIG_PUSH_COUNT registered, But tps control manager is no limit implementation.

2025-06-19 20:11:01,252 INFO No tps control rule of CONFIG_PUSH_SUCCESS found  

2025-06-19 20:11:01,252 WARN Tps point for CONFIG_PUSH_SUCCESS registered, But tps control manager is no limit implementation.

2025-06-19 20:11:01,252 INFO No tps control rule of CONFIG_PUSH_FAIL found  

2025-06-19 20:11:01,252 WARN Tps point for CONFIG_PUSH_FAIL registered, But tps control manager is no limit implementation.

2025-06-19 20:11:01,292 INFO Ready to get current node abilities...

2025-06-19 20:11:01,295 INFO Ready to initialize current node abilities, support modes: [SERVER, CLUSTER_CLIENT, SDK_CLIENT]

2025-06-19 20:11:01,296 INFO Initialize current abilities finish...

2025-06-19 20:11:01,297 INFO Ready to get current node abilities...

2025-06-19 20:11:01,298 INFO Ready to initialize current node abilities, support modes: [SDK_CLIENT]

2025-06-19 20:11:01,298 INFO Initialize current abilities finish...

2025-06-19 20:11:01,298 INFO [AbilityControlManager] Successfully initialize AbilityControlManager

2025-06-19 20:11:02,174 INFO Connection check task start

2025-06-19 20:11:02,180 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:02,180 INFO Out dated connection ,size=0

2025-06-19 20:11:02,181 INFO Connection check task end

2025-06-19 20:11:03,044 INFO Adding welcome page: class path resource [static/index.html]

2025-06-19 20:11:03,506 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-06-19 20:11:03,508 INFO Will not secure Ant [pattern='/**']

2025-06-19 20:11:03,532 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7a1b8a46, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2921199d, org.springframework.security.web.context.SecurityContextPersistenceFilter@679dd234, org.springframework.security.web.header.HeaderWriterFilter@da4cf09, org.springframework.security.web.csrf.CsrfFilter@16134476, org.springframework.security.web.authentication.logout.LogoutFilter@47829d6d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1e5eb20a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@74b86971, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3d40a3b4, org.springframework.security.web.session.SessionManagementFilter@6f5d0190, org.springframework.security.web.access.ExceptionTranslationFilter@62b09715]

2025-06-19 20:11:03,567 INFO Exposing 1 endpoint(s) beneath base path '/actuator'

2025-06-19 20:11:03,614 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-06-19 20:11:03,630 INFO No tps control rule of NamingInstanceUpdate found  

2025-06-19 20:11:03,630 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,630 INFO No tps control rule of NamingServiceDeregister found  

2025-06-19 20:11:03,630 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of ConfigQuery found  

2025-06-19 20:11:03,631 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of NamingInstanceMetadataUpdate found  

2025-06-19 20:11:03,631 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of ConfigPublish found  

2025-06-19 20:11:03,631 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 WARN Tps point for NamingServiceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of NamingInstanceDeregister found  

2025-06-19 20:11:03,631 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 WARN Tps point for NamingInstanceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of NamingServiceRegister found  

2025-06-19 20:11:03,631 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,631 INFO No tps control rule of NamingServiceUpdate found  

2025-06-19 20:11:03,631 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 WARN Tps point for NamingInstanceMetadataUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 INFO No tps control rule of NamingServiceQuery found  

2025-06-19 20:11:03,632 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 WARN Tps point for NamingServiceUpdate registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 INFO No tps control rule of NamingInstanceQuery found  

2025-06-19 20:11:03,632 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 INFO No tps control rule of NamingServiceListQuery found  

2025-06-19 20:11:03,632 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 INFO No tps control rule of NamingInstanceRegister found  

2025-06-19 20:11:03,632 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,632 WARN Tps point for NamingServiceRegister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 INFO No tps control rule of NamingServiceSubscribe found  

2025-06-19 20:11:03,633 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingServiceSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingInstanceRegister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 INFO No tps control rule of HttpHealthCheck found  

2025-06-19 20:11:03,633 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for HttpHealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingInstanceQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,633 WARN Tps point for NamingInstanceDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,634 WARN Tps point for ConfigQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,635 INFO No tps control rule of ClusterConfigChangeNotify found  

2025-06-19 20:11:03,635 WARN Tps point for ClusterConfigChangeNotify registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,635 INFO No tps control rule of ConfigListen found  

2025-06-19 20:11:03,635 WARN Tps point for ConfigListen registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,635 INFO No tps control rule of ConfigRemove found  

2025-06-19 20:11:03,635 WARN Tps point for ConfigRemove registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,636 WARN Tps point for ConfigPublish registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,636 INFO No tps control rule of HealthCheck found  

2025-06-19 20:11:03,636 WARN Tps point for HealthCheck registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,636 INFO No tps control rule of RemoteNamingServiceQuery found  

2025-06-19 20:11:03,636 WARN Tps point for RemoteNamingServiceQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,636 INFO No tps control rule of RemoteNamingInstanceBatchRegister found  

2025-06-19 20:11:03,636 WARN Tps point for RemoteNamingInstanceBatchRegister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,637 INFO No tps control rule of RemoteNamingInstanceRegisterDeregister found  

2025-06-19 20:11:03,637 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,637 INFO No tps control rule of RemoteNamingServiceListQuery found  

2025-06-19 20:11:03,637 WARN Tps point for RemoteNamingServiceListQuery registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,637 INFO No tps control rule of RemoteNamingServiceSubscribeUnSubscribe found  

2025-06-19 20:11:03,637 WARN Tps point for RemoteNamingServiceSubscribeUnSubscribe registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,637 WARN Tps point for RemoteNamingInstanceRegisterDeregister registered, But tps control manager is no limit implementation.

2025-06-19 20:11:03,641 INFO Started Nacos in 8.372 seconds (JVM running for 8.833)

2025-06-19 20:11:03,641 INFO Nacos started successfully in stand alone mode. use embedded storage

2025-06-19 20:11:04,253 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:05,190 INFO Connection check task start

2025-06-19 20:11:05,190 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:05,190 INFO Out dated connection ,size=0

2025-06-19 20:11:05,190 INFO Connection check task end

2025-06-19 20:11:07,262 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:08,194 INFO Connection check task start

2025-06-19 20:11:08,194 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:08,194 INFO Out dated connection ,size=0

2025-06-19 20:11:08,194 INFO Connection check task end

2025-06-19 20:11:10,278 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:11,198 INFO Connection check task start

2025-06-19 20:11:11,198 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:11,198 INFO Out dated connection ,size=0

2025-06-19 20:11:11,198 INFO Connection check task end

2025-06-19 20:11:13,289 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:14,199 INFO Connection check task start

2025-06-19 20:11:14,199 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:14,199 INFO Out dated connection ,size=0

2025-06-19 20:11:14,199 INFO Connection check task end

2025-06-19 20:11:16,296 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:17,209 INFO Connection check task start

2025-06-19 20:11:17,209 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:17,209 INFO Out dated connection ,size=0

2025-06-19 20:11:17,209 INFO Connection check task end

2025-06-19 20:11:19,304 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:20,220 INFO Connection check task start

2025-06-19 20:11:20,220 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:20,220 INFO Out dated connection ,size=0

2025-06-19 20:11:20,220 INFO Connection check task end

2025-06-19 20:11:22,316 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:23,233 INFO Connection check task start

2025-06-19 20:11:23,233 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:23,233 INFO Out dated connection ,size=0

2025-06-19 20:11:23,234 INFO Connection check task end

2025-06-19 20:11:25,320 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:26,250 INFO Connection check task start

2025-06-19 20:11:26,251 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:26,251 INFO Out dated connection ,size=0

2025-06-19 20:11:26,251 INFO Connection check task end

2025-06-19 20:11:28,321 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:29,261 INFO Connection check task start

2025-06-19 20:11:29,261 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:29,261 INFO Out dated connection ,size=0

2025-06-19 20:11:29,261 INFO Connection check task end

2025-06-19 20:11:31,323 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:32,277 INFO Connection check task start

2025-06-19 20:11:32,277 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:32,277 INFO Out dated connection ,size=0

2025-06-19 20:11:32,277 INFO Connection check task end

2025-06-19 20:11:34,324 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:35,282 INFO Connection check task start

2025-06-19 20:11:35,282 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:35,282 INFO Out dated connection ,size=0

2025-06-19 20:11:35,282 INFO Connection check task end

2025-06-19 20:11:37,333 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:38,287 INFO Connection check task start

2025-06-19 20:11:38,287 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:38,287 INFO Out dated connection ,size=0

2025-06-19 20:11:38,287 INFO Connection check task end

2025-06-19 20:11:40,341 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:41,292 INFO Connection check task start

2025-06-19 20:11:41,292 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:41,292 INFO Out dated connection ,size=0

2025-06-19 20:11:41,292 INFO Connection check task end

2025-06-19 20:11:43,349 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:44,302 INFO Connection check task start

2025-06-19 20:11:44,302 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:44,302 INFO Out dated connection ,size=0

2025-06-19 20:11:44,302 INFO Connection check task end

2025-06-19 20:11:46,361 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:47,315 INFO Connection check task start

2025-06-19 20:11:47,315 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:47,315 INFO Out dated connection ,size=0

2025-06-19 20:11:47,315 INFO Connection check task end

2025-06-19 20:11:49,362 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:50,327 INFO Connection check task start

2025-06-19 20:11:50,327 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:50,327 INFO Out dated connection ,size=0

2025-06-19 20:11:50,327 INFO Connection check task end

2025-06-19 20:11:52,374 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:53,341 INFO Connection check task start

2025-06-19 20:11:53,341 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:53,341 INFO Out dated connection ,size=0

2025-06-19 20:11:53,341 INFO Connection check task end

2025-06-19 20:11:55,383 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:56,349 INFO Connection check task start

2025-06-19 20:11:56,349 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:56,349 INFO Out dated connection ,size=0

2025-06-19 20:11:56,349 INFO Connection check task end

2025-06-19 20:11:58,394 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:11:59,357 INFO Connection check task start

2025-06-19 20:11:59,357 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:11:59,357 INFO Out dated connection ,size=0

2025-06-19 20:11:59,357 INFO Connection check task end

2025-06-19 20:12:01,398 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:02,363 INFO Connection check task start

2025-06-19 20:12:02,363 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:02,363 INFO Out dated connection ,size=0

2025-06-19 20:12:02,363 INFO Connection check task end

2025-06-19 20:12:04,402 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:05,366 INFO Connection check task start

2025-06-19 20:12:05,366 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:05,366 INFO Out dated connection ,size=0

2025-06-19 20:12:05,366 INFO Connection check task end

2025-06-19 20:12:07,415 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:08,381 INFO Connection check task start

2025-06-19 20:12:08,381 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:08,381 INFO Out dated connection ,size=0

2025-06-19 20:12:08,381 INFO Connection check task end

2025-06-19 20:12:10,421 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:11,390 INFO Connection check task start

2025-06-19 20:12:11,390 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:11,390 INFO Out dated connection ,size=0

2025-06-19 20:12:11,390 INFO Connection check task end

2025-06-19 20:12:13,433 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:14,391 INFO Connection check task start

2025-06-19 20:12:14,391 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:14,391 INFO Out dated connection ,size=0

2025-06-19 20:12:14,391 INFO Connection check task end

2025-06-19 20:12:16,443 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:17,404 INFO Connection check task start

2025-06-19 20:12:17,404 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:17,404 INFO Out dated connection ,size=0

2025-06-19 20:12:17,404 INFO Connection check task end

2025-06-19 20:12:19,451 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:20,413 INFO Connection check task start

2025-06-19 20:12:20,413 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:20,413 INFO Out dated connection ,size=0

2025-06-19 20:12:20,413 INFO Connection check task end

2025-06-19 20:12:22,461 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:23,423 INFO Connection check task start

2025-06-19 20:12:23,423 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:23,423 INFO Out dated connection ,size=0

2025-06-19 20:12:23,423 INFO Connection check task end

2025-06-19 20:12:25,475 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:26,426 INFO Connection check task start

2025-06-19 20:12:26,426 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:26,426 INFO Out dated connection ,size=0

2025-06-19 20:12:26,426 INFO Connection check task end

2025-06-19 20:12:28,483 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:29,436 INFO Connection check task start

2025-06-19 20:12:29,436 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:29,436 INFO Out dated connection ,size=0

2025-06-19 20:12:29,436 INFO Connection check task end

2025-06-19 20:12:31,495 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:32,449 INFO Connection check task start

2025-06-19 20:12:32,449 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:32,449 INFO Out dated connection ,size=0

2025-06-19 20:12:32,449 INFO Connection check task end

2025-06-19 20:12:34,498 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:35,452 INFO Connection check task start

2025-06-19 20:12:35,452 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:35,452 INFO Out dated connection ,size=0

2025-06-19 20:12:35,452 INFO Connection check task end

2025-06-19 20:12:37,507 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:38,463 INFO Connection check task start

2025-06-19 20:12:38,463 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:38,463 INFO Out dated connection ,size=0

2025-06-19 20:12:38,463 INFO Connection check task end

2025-06-19 20:12:40,520 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:41,477 INFO Connection check task start

2025-06-19 20:12:41,477 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:41,477 INFO Out dated connection ,size=0

2025-06-19 20:12:41,477 INFO Connection check task end

2025-06-19 20:12:43,534 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:44,481 INFO Connection check task start

2025-06-19 20:12:44,481 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:44,481 INFO Out dated connection ,size=0

2025-06-19 20:12:44,481 INFO Connection check task end

2025-06-19 20:12:46,544 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:47,490 INFO Connection check task start

2025-06-19 20:12:47,490 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:47,490 INFO Out dated connection ,size=0

2025-06-19 20:12:47,490 INFO Connection check task end

2025-06-19 20:12:49,555 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:50,492 INFO Connection check task start

2025-06-19 20:12:50,492 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:50,492 INFO Out dated connection ,size=0

2025-06-19 20:12:50,492 INFO Connection check task end

2025-06-19 20:12:52,564 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:53,498 INFO Connection check task start

2025-06-19 20:12:53,498 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:53,498 INFO Out dated connection ,size=0

2025-06-19 20:12:53,498 INFO Connection check task end

2025-06-19 20:12:55,568 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:56,506 INFO Connection check task start

2025-06-19 20:12:56,506 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:56,506 INFO Out dated connection ,size=0

2025-06-19 20:12:56,506 INFO Connection check task end

2025-06-19 20:12:58,570 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:12:59,508 INFO Connection check task start

2025-06-19 20:12:59,508 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:12:59,508 INFO Out dated connection ,size=0

2025-06-19 20:12:59,508 INFO Connection check task end

2025-06-19 20:13:01,581 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:02,520 INFO Connection check task start

2025-06-19 20:13:02,520 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:02,520 INFO Out dated connection ,size=0

2025-06-19 20:13:02,520 INFO Connection check task end

2025-06-19 20:13:04,590 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:05,529 INFO Connection check task start

2025-06-19 20:13:05,529 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:05,529 INFO Out dated connection ,size=0

2025-06-19 20:13:05,529 INFO Connection check task end

2025-06-19 20:13:07,591 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:08,540 INFO Connection check task start

2025-06-19 20:13:08,540 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:08,540 INFO Out dated connection ,size=0

2025-06-19 20:13:08,540 INFO Connection check task end

2025-06-19 20:13:10,595 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:11,546 INFO Connection check task start

2025-06-19 20:13:11,546 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:11,546 INFO Out dated connection ,size=0

2025-06-19 20:13:11,546 INFO Connection check task end

2025-06-19 20:13:13,602 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:14,552 INFO Connection check task start

2025-06-19 20:13:14,552 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:14,552 INFO Out dated connection ,size=0

2025-06-19 20:13:14,552 INFO Connection check task end

2025-06-19 20:13:16,616 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:17,558 INFO Connection check task start

2025-06-19 20:13:17,558 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:17,558 INFO Out dated connection ,size=0

2025-06-19 20:13:17,558 INFO Connection check task end

2025-06-19 20:13:19,618 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:20,571 INFO Connection check task start

2025-06-19 20:13:20,571 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:20,571 INFO Out dated connection ,size=0

2025-06-19 20:13:20,571 INFO Connection check task end

2025-06-19 20:13:22,619 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:23,580 INFO Connection check task start

2025-06-19 20:13:23,580 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:23,580 INFO Out dated connection ,size=0

2025-06-19 20:13:23,580 INFO Connection check task end

2025-06-19 20:13:25,623 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:26,590 INFO Connection check task start

2025-06-19 20:13:26,590 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:26,590 INFO Out dated connection ,size=0

2025-06-19 20:13:26,590 INFO Connection check task end

2025-06-19 20:13:28,630 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:29,591 INFO Connection check task start

2025-06-19 20:13:29,591 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:29,591 INFO Out dated connection ,size=0

2025-06-19 20:13:29,591 INFO Connection check task end

2025-06-19 20:13:31,633 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:32,602 INFO Connection check task start

2025-06-19 20:13:32,602 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:32,602 INFO Out dated connection ,size=0

2025-06-19 20:13:32,602 INFO Connection check task end

2025-06-19 20:13:34,642 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:35,615 INFO Connection check task start

2025-06-19 20:13:35,615 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:35,615 INFO Out dated connection ,size=0

2025-06-19 20:13:35,615 INFO Connection check task end

2025-06-19 20:13:37,651 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:38,628 INFO Connection check task start

2025-06-19 20:13:38,628 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:38,628 INFO Out dated connection ,size=0

2025-06-19 20:13:38,628 INFO Connection check task end

2025-06-19 20:13:40,664 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:41,643 INFO Connection check task start

2025-06-19 20:13:41,643 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:41,643 INFO Out dated connection ,size=0

2025-06-19 20:13:41,643 INFO Connection check task end

2025-06-19 20:13:43,670 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:44,657 INFO Connection check task start

2025-06-19 20:13:44,657 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:44,657 INFO Out dated connection ,size=0

2025-06-19 20:13:44,657 INFO Connection check task end

2025-06-19 20:13:46,672 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:47,668 INFO Connection check task start

2025-06-19 20:13:47,668 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:47,668 INFO Out dated connection ,size=0

2025-06-19 20:13:47,668 INFO Connection check task end

2025-06-19 20:13:49,677 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:50,674 INFO Connection check task start

2025-06-19 20:13:50,674 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:50,674 INFO Out dated connection ,size=0

2025-06-19 20:13:50,674 INFO Connection check task end

2025-06-19 20:13:52,691 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:53,676 INFO Connection check task start

2025-06-19 20:13:53,676 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:53,676 INFO Out dated connection ,size=0

2025-06-19 20:13:53,676 INFO Connection check task end

2025-06-19 20:13:55,698 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:56,679 INFO Connection check task start

2025-06-19 20:13:56,679 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:56,679 INFO Out dated connection ,size=0

2025-06-19 20:13:56,679 INFO Connection check task end

2025-06-19 20:13:58,711 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:13:59,693 INFO Connection check task start

2025-06-19 20:13:59,693 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:13:59,693 INFO Out dated connection ,size=0

2025-06-19 20:13:59,693 INFO Connection check task end

2025-06-19 20:14:01,713 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:02,703 INFO Connection check task start

2025-06-19 20:14:02,703 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:02,703 INFO Out dated connection ,size=0

2025-06-19 20:14:02,703 INFO Connection check task end

2025-06-19 20:14:04,718 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:05,717 INFO Connection check task start

2025-06-19 20:14:05,717 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:05,717 INFO Out dated connection ,size=0

2025-06-19 20:14:05,717 INFO Connection check task end

2025-06-19 20:14:07,729 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:08,721 INFO Connection check task start

2025-06-19 20:14:08,721 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:08,721 INFO Out dated connection ,size=0

2025-06-19 20:14:08,721 INFO Connection check task end

2025-06-19 20:14:10,743 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:11,723 INFO Connection check task start

2025-06-19 20:14:11,723 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:11,723 INFO Out dated connection ,size=0

2025-06-19 20:14:11,723 INFO Connection check task end

2025-06-19 20:14:13,749 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:14,730 INFO Connection check task start

2025-06-19 20:14:14,730 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:14,730 INFO Out dated connection ,size=0

2025-06-19 20:14:14,730 INFO Connection check task end

2025-06-19 20:14:16,750 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:17,732 INFO Connection check task start

2025-06-19 20:14:17,732 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:17,732 INFO Out dated connection ,size=0

2025-06-19 20:14:17,732 INFO Connection check task end

2025-06-19 20:14:19,752 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:20,736 INFO Connection check task start

2025-06-19 20:14:20,736 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:20,736 INFO Out dated connection ,size=0

2025-06-19 20:14:20,736 INFO Connection check task end

2025-06-19 20:14:22,761 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:23,749 INFO Connection check task start

2025-06-19 20:14:23,749 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:23,749 INFO Out dated connection ,size=0

2025-06-19 20:14:23,749 INFO Connection check task end

2025-06-19 20:14:25,768 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:26,750 INFO Connection check task start

2025-06-19 20:14:26,750 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:26,750 INFO Out dated connection ,size=0

2025-06-19 20:14:26,750 INFO Connection check task end

2025-06-19 20:14:28,770 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:29,765 INFO Connection check task start

2025-06-19 20:14:29,765 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:29,765 INFO Out dated connection ,size=0

2025-06-19 20:14:29,765 INFO Connection check task end

2025-06-19 20:14:31,781 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:32,770 INFO Connection check task start

2025-06-19 20:14:32,770 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:32,770 INFO Out dated connection ,size=0

2025-06-19 20:14:32,770 INFO Connection check task end

2025-06-19 20:14:34,785 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:35,781 INFO Connection check task start

2025-06-19 20:14:35,781 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:35,781 INFO Out dated connection ,size=0

2025-06-19 20:14:35,781 INFO Connection check task end

2025-06-19 20:14:37,798 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:38,791 INFO Connection check task start

2025-06-19 20:14:38,791 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:38,791 INFO Out dated connection ,size=0

2025-06-19 20:14:38,791 INFO Connection check task end

2025-06-19 20:14:40,806 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:41,804 INFO Connection check task start

2025-06-19 20:14:41,804 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:41,804 INFO Out dated connection ,size=0

2025-06-19 20:14:41,804 INFO Connection check task end

2025-06-19 20:14:43,815 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:44,437 INFO new connection registered successfully, connectionId = 1750335284315_127.0.0.1_60342,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=60342, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1750335284315_127.0.0.1_60342', createTime=Thu Jun 19 20:14:44 CST 2025, lastActiveTime=1750335284431, appName='unknown', tenant='studentmis-v2', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-06-19 20:14:44,556 INFO Get ParamCheck config from env, ParamCheckConfig{paramCheckEnabled=trueactiveParamChecker=default}

2025-06-19 20:14:44,810 INFO Connection check task start

2025-06-19 20:14:44,810 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:14:44,810 INFO Out dated connection ,size=0

2025-06-19 20:14:44,810 INFO Connection check task end

2025-06-19 20:14:46,816 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:14:46,963 INFO [1750335284315_127.0.0.1_60342]Connection unregistered successfully. 

2025-06-19 20:14:47,821 INFO Connection check task start

2025-06-19 20:14:47,821 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:47,821 INFO Out dated connection ,size=0

2025-06-19 20:14:47,821 INFO Connection check task end

2025-06-19 20:14:49,818 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:50,834 INFO Connection check task start

2025-06-19 20:14:50,834 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:50,834 INFO Out dated connection ,size=0

2025-06-19 20:14:50,834 INFO Connection check task end

2025-06-19 20:14:52,819 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:53,835 INFO Connection check task start

2025-06-19 20:14:53,835 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:53,835 INFO Out dated connection ,size=0

2025-06-19 20:14:53,835 INFO Connection check task end

2025-06-19 20:14:55,823 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:56,850 INFO Connection check task start

2025-06-19 20:14:56,850 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:56,850 INFO Out dated connection ,size=0

2025-06-19 20:14:56,850 INFO Connection check task end

2025-06-19 20:14:58,837 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:14:59,856 INFO Connection check task start

2025-06-19 20:14:59,856 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:14:59,856 INFO Out dated connection ,size=0

2025-06-19 20:14:59,856 INFO Connection check task end

2025-06-19 20:15:01,853 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:15:02,869 INFO Connection check task start

2025-06-19 20:15:02,869 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:15:02,869 INFO Out dated connection ,size=0

2025-06-19 20:15:02,869 INFO Connection check task end

2025-06-19 20:15:04,863 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:15:05,880 INFO Connection check task start

2025-06-19 20:15:05,880 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:15:05,880 INFO Out dated connection ,size=0

2025-06-19 20:15:05,880 INFO Connection check task end

2025-06-19 20:15:07,866 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:15:08,886 INFO Connection check task start

2025-06-19 20:15:08,886 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:15:08,886 INFO Out dated connection ,size=0

2025-06-19 20:15:08,886 INFO Connection check task end

2025-06-19 20:15:10,878 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:15:11,901 INFO Connection check task start

2025-06-19 20:15:11,901 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:15:11,901 INFO Out dated connection ,size=0

2025-06-19 20:15:11,901 INFO Connection check task end

2025-06-19 20:15:13,880 INFO ConnectionMetrics, totalCount = 0, detail = {long_connection=0, long_polling=0}

2025-06-19 20:15:14,917 INFO Connection check task start

2025-06-19 20:15:14,917 INFO Long connection metrics detail ,Total count =0, sdkCount=0,clusterCount=0

2025-06-19 20:15:14,917 INFO Out dated connection ,size=0

2025-06-19 20:15:14,917 INFO Connection check task end

2025-06-19 20:15:15,026 INFO new connection registered successfully, connectionId = 1750335314957_127.0.0.1_60465,connection=Connection{traced=false, abilities=null, metaInfo=ConnectionMeta{connectType='GRPC', clientIp='*************', remoteIp='127.0.0.1', remotePort=60465, localPort=9848, version='Nacos-Java-Client:v2.2.1', connectionId='1750335314957_127.0.0.1_60465', createTime=Thu Jun 19 20:15:15 CST 2025, lastActiveTime=1750335315026, appName='unknown', tenant='studentmis-v2', labels={source=sdk, taskId=0, module=config, AppName=unknown}}} 

2025-06-19 20:15:16,893 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:17,927 INFO Connection check task start

2025-06-19 20:15:17,927 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:17,927 INFO Out dated connection ,size=0

2025-06-19 20:15:17,927 INFO Connection check task end

2025-06-19 20:15:19,906 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:20,932 INFO Connection check task start

2025-06-19 20:15:20,932 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:20,932 INFO Out dated connection ,size=0

2025-06-19 20:15:20,932 INFO Connection check task end

2025-06-19 20:15:22,920 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:23,939 INFO Connection check task start

2025-06-19 20:15:23,939 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:23,939 INFO Out dated connection ,size=0

2025-06-19 20:15:23,939 INFO Connection check task end

2025-06-19 20:15:25,924 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:26,953 INFO Connection check task start

2025-06-19 20:15:26,953 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:26,953 INFO Out dated connection ,size=0

2025-06-19 20:15:26,953 INFO Connection check task end

2025-06-19 20:15:28,938 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:29,957 INFO Connection check task start

2025-06-19 20:15:29,957 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:29,957 INFO Out dated connection ,size=0

2025-06-19 20:15:29,957 INFO Connection check task end

2025-06-19 20:15:31,939 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:32,968 INFO Connection check task start

2025-06-19 20:15:32,968 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:32,968 INFO Out dated connection ,size=0

2025-06-19 20:15:32,968 INFO Connection check task end

2025-06-19 20:15:34,951 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:35,985 INFO Connection check task start

2025-06-19 20:15:35,985 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:35,985 INFO Out dated connection ,size=0

2025-06-19 20:15:35,985 INFO Connection check task end

2025-06-19 20:15:37,967 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:38,997 INFO Connection check task start

2025-06-19 20:15:38,997 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:38,997 INFO Out dated connection ,size=0

2025-06-19 20:15:38,997 INFO Connection check task end

2025-06-19 20:15:40,968 INFO ConnectionMetrics, totalCount = 1, detail = {long_connection=1, long_polling=0}

2025-06-19 20:15:42,000 INFO Connection check task start

2025-06-19 20:15:42,000 INFO Long connection metrics detail ,Total count =1, sdkCount=1,clusterCount=0

2025-06-19 20:15:42,000 INFO Out dated connection ,size=0

2025-06-19 20:15:42,000 INFO Connection check task end

